"use client";

import { SidebarTrigger } from "@/components/_common/header/sidebar-trigger";
import { CartDropdown } from "@/components/_common/header/cart-dropdown";
import { ProfileDropdown } from "@/components/_common/header/profile-dropdown";
import { LoadingScreen } from "@/components/_common/loading-screen";
import { useCustomer } from "@/hooks/use-customer";
import { useLogout } from "@/hooks/use-logout";
import { JourneyRuleContainer } from "@/hooks/use-page-rules";
import {
  getAccessedJourney,
  getCartCookie,
  setCartCookie,
  ShoppingCartProvider,
  useShoppingCart,
} from "@/hooks/use-shopping-cart";
import { usePathname } from "@/i18n/navigation";
import {
  createShoppingCart,
  getPurchasedShoppingCartByEmail,
  getShoppingCartByEmail,
  getShoppingCartById,
} from "@/lib/api/shoppingCart";
import { ShoppingCartJourney } from "@/lib/api/shoppingCart/types";
import { JourneyType } from "@/utils/journeys";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { ReactNode, useEffect, useRef } from "react";

interface ShoppingCartFnProps {
  email?: string;
  accessedJourney: JourneyType | null;
  pathname: string;
}

async function shoppingCartFn({ email, accessedJourney, pathname }: ShoppingCartFnProps) {
  try {
    const cart = await (async () => {
      if (!email) {
        const cartId = getCartCookie();

        if (!cartId) return null;

        const cartById = await getShoppingCartById(cartId);

        if (!cartById) return null;

        return cartById;
      }

      if (pathname.includes("conclusion")) {
        const customerCart = await getPurchasedShoppingCartByEmail(email);

        if (!customerCart) return null;

        return customerCart;
      }

      const customerCart = await getShoppingCartByEmail(email);

      if (!customerCart) return null;

      return customerCart;
    })();

    if (!accessedJourney) accessedJourney = cart?.journey || "LONG";

    if (!cart) {
      const createdCart = await createShoppingCart({
        email: email || undefined,
        journey: accessedJourney as ShoppingCartJourney,
      });

      setCartCookie(createdCart.id);

      return createdCart;
    }

    setCartCookie(cart.id);

    return cart;
  } catch (err: any) {
    return null;
  }
}

export function ShopHeaderContent() {
  const { customer } = useCustomer();

  const { shoppingCart } = useShoppingCart();

  return (
    <>
      <CartDropdown shoppingCart={shoppingCart} />
      <ProfileDropdown />
      {!!customer?.contracts?.length && <SidebarTrigger />}
    </>
  );
}

export function ShopWrapper({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const session = useSession();
  const accessedJourney = getAccessedJourney(pathname);
  const { logout } = useLogout();
  const { isLoading: isCustomerLoading } = useCustomer();

  const lastSession = useRef(session);
  const userEmail = session.data?.user.email || undefined;

  const shoppingCartQuery = useQuery({
    queryKey: ["shopping-cart", { email: userEmail, journey: accessedJourney }],
    queryFn: () => shoppingCartFn({ email: userEmail, accessedJourney, pathname }),
    enabled: session.status !== "loading" && !isCustomerLoading,
  });

  const isShoppingCartLoading = shoppingCartQuery.isPending || shoppingCartQuery.isLoading || !shoppingCartQuery.data;

  useEffect(() => {
    if (lastSession.current.status === "authenticated" && session.status === "unauthenticated") {
      logout();
      return;
    }

    if (session.status !== lastSession.current.status) lastSession.current = session;
  }, [session]);

  if (isShoppingCartLoading) return <LoadingScreen />;

  return (
    <ShoppingCartProvider shoppingCart={shoppingCartQuery.data!} email={userEmail} journey={accessedJourney}>
      <JourneyRuleContainer>{children}</JourneyRuleContainer>
    </ShoppingCartProvider>
  );
}
