"use client";

import { Button } from "@interzero/oneepr-react-ui/Button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { ChevronLeft } from "lucide-react";

interface UnsavedChangesBackButtonProps {
  hasUnsavedChanges: boolean;
  onBack: () => void;
  buttonText?: string;
  dialogTitle?: string;
  dialogDescription?: string;
  leaveButtonText?: string;
}

export function UnsavedChangesBackButton({
  hasUnsavedChanges,
  onBack,
  buttonText = "Back",
  dialogTitle = "Are you sure you want to go back?",
  dialogDescription = "Any unsaved changes will be lost if you leave this page.",
  leaveButtonText = "Leave page",
}: UnsavedChangesBackButtonProps) {
  const buttonContent = (
    <Button variant="text" color="light-blue" size="medium" leadingIcon={<ChevronLeft className="size-5" />}>
      {buttonText}
    </Button>
  );

  if (hasUnsavedChanges) {
    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>{buttonContent}</AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{dialogTitle}</AlertDialogTitle>
            <AlertDialogDescription>{dialogDescription}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction color="yellow" onClick={onBack}>
              {leaveButtonText}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return (
    <Button
      variant="text"
      color="light-blue"
      size="medium"
      leadingIcon={<ChevronLeft className="size-5" />}
      onClick={onBack}
    >
      {buttonText}
    </Button>
  );
}
