import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CreateCriteria } from "@/types/service-setup/criteria";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import { CRITERIA_CALCULATOR_TYPES, CRITERIA_INPUT_TYPES, CriteriasFormData, invalidateSubQueries } from ".";
import { Add, Delete } from "@interzero/oneepr-react-ui/Icon";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useMutation } from "@tanstack/react-query";
import { deleteCriteria } from "@/lib/api/criterias";
import { FractionInput } from "@/components/ui/fraction-input";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { enqueueSnackbar } from "notistack";
import { CharacterCounter, CHARACTER_COUNTER_DEFAULTS } from "@/components/ui/character-counter";
import { capitalizeFirstLetter } from "@/utils/capitalize-first-letter";

export type FormCriteria = Omit<CreateCriteria, "options"> & {
  id?: number;
  options: { id?: number; option_value: string; option_to_value: string | null; value: string }[];
};

interface CriteriaCardProps {
  criteriaIndex: number;
  criteria: FormCriteria;
  optionValues: { value: string; label: string }[];
  onUpdate: (index: number, data: Partial<FormCriteria>) => void;
  onRemove: (index: number) => void;
  packagingServiceId: number | undefined;
  requiredInformationId: number | undefined;
}

export function CriteriaCard({
  criteriaIndex,
  criteria,
  optionValues,
  onRemove,
  onUpdate,
  packagingServiceId,
  requiredInformationId,
}: CriteriaCardProps) {
  const {
    register,
    control,
    setValue,
    formState: { errors },
  } = useFormContext<CriteriasFormData>();

  const { mutate: removeCriteria, isPending: isDeletingCriteria } = useMutation({
    mutationFn: (criteriaId: number) => deleteCriteria(criteriaId),
  });

  const { country } = useServiceSetup();

  function handleRemoveCriteria(e: React.MouseEvent<HTMLButtonElement>) {
    if (criteria.id) {
      e.preventDefault();

      return removeCriteria(criteria.id, {
        onSuccess: () => {
          onRemove(criteriaIndex);
          invalidateSubQueries(criteria.type, country.code, packagingServiceId, requiredInformationId);
          enqueueSnackbar("Criteria removed successfully", { variant: "success" });
        },
        onError: () => {
          enqueueSnackbar("Failed to remove criteria. Please try again.", { variant: "error" });
        },
      });
    }

    onRemove(criteriaIndex);
  }

  function handleAddSelectOption() {
    onUpdate(criteriaIndex, {
      options: [...options, { option_value: "", option_to_value: null, value: "" }],
    });
  }

  function handleRemoveSelectOption(optionIndex: number) {
    onUpdate(criteriaIndex, {
      options: options.filter((_, index) => index !== optionIndex),
    });
  }

  const inputType = useWatch({
    control,
    name: `criterias.${criteriaIndex}.input_type`,
    defaultValue: "YES_NO",
  });

  const calculatorType = useWatch({
    control,
    name: `criterias.${criteriaIndex}.calculator_type`,
    defaultValue: "LICENSE_FEES",
  });

  function handleInputTypeChange(value: string) {
    let DEFAULT_OPTIONS: { option_value: string; option_to_value: string | null; value: string }[] = [];

    if (value === "YES_NO") {
      DEFAULT_OPTIONS = [
        { option_value: "YES", option_to_value: null, value: optionValues[0]?.value || "" },
        { option_value: "NO", option_to_value: null, value: optionValues[1]?.value || "" },
      ];
    }

    setValue(`criterias.${criteriaIndex}.options`, DEFAULT_OPTIONS);
    onUpdate(criteriaIndex, {
      options: DEFAULT_OPTIONS,
    });
  }

  const options =
    useWatch({
      control,
      name: `criterias.${criteriaIndex}.options`,
    }) || [];
  const helpTextValue = useWatch({
    control,
    name: `criterias.${criteriaIndex}.help_text`,
    defaultValue: "",
  });
  const titleTextValue = useWatch({
    control,
    name: `criterias.${criteriaIndex}.title`,
    defaultValue: "",
  });
  return (
    <div className="bg-background p-6 rounded-[20px]">
      {criteria.mode === "CALCULATOR" ? (
        <div className="font-bold inline-block px-4 py-2 rounded-xl bg-secondary text-primary">
          Criteria on Calculator
        </div>
      ) : (
        <div className="font-bold inline-block px-4 py-2 rounded-xl bg-[#FFF5CC] text-[#997C00]">
          Additional assessment criteria
        </div>
      )}
      <div className="flex items-center justify-between py-2">
        <p className="text-tonal-dark-cream-10 font-bold text-xl">
          {criteria.mode === "CALCULATOR" ? "Criteria on Calculator" : "Question"}
        </p>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <div>
              <Button type="button" variant="text" color="red" size="small">
                Remove criteria
              </Button>
            </div>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleRemoveCriteria} disabled={isDeletingCriteria}>
                {isDeletingCriteria ? "Removing..." : "Remove"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      {criteria.mode === "CALCULATOR" && (
        <div className="space-y-6">
          <div className="grid grid-cols-3 gap-6">
            <div className="col-span-1">
              <div className="space-y-2">
                <label></label>
                <Controller
                  control={control}
                  name={`criterias.${criteriaIndex}.calculator_type`}
                  render={({ field: { value, onChange } }) => (
                    <Select
                      defaultValue={CRITERIA_CALCULATOR_TYPES[0].value}
                      value={value as string}
                      onValueChange={onChange}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {CRITERIA_CALCULATOR_TYPES.map((calculatorType) => (
                          <SelectItem key={calculatorType.value} value={calculatorType.value}>
                            {calculatorType.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
          </div>
          <div className="my-6 space-y-8">
            <div className="space-y-5">
              {options.map((_, optionIndex) => (
                <div key={optionIndex}>
                  <div className="flex items-center gap-4">
                    <div className="w-32">
                      <Controller
                        name={`criterias.${criteriaIndex}.options.${optionIndex}.option_value`}
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <FractionInput
                            value={Number(value) || 0}
                            onChange={(e) => onChange(String(e))}
                            type={calculatorType === "LICENSE_FEES" ? "currency" : "weight"}
                            error={errors.criterias?.[criteriaIndex]?.options?.[optionIndex]?.option_value?.message}
                          />
                        )}
                      />
                    </div>
                    <p className="text-tonal-dark-cream-20">to</p>
                    <div className="w-32">
                      <Controller
                        name={`criterias.${criteriaIndex}.options.${optionIndex}.option_to_value`}
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <FractionInput
                            value={Number(value) || 0}
                            onChange={(e) => onChange(String(e))}
                            type={calculatorType === "LICENSE_FEES" ? "currency" : "weight"}
                            error={errors.criterias?.[criteriaIndex]?.options?.[optionIndex]?.option_to_value?.message}
                          />
                        )}
                      />
                    </div>
                    <p className="text-tonal-dark-cream-20">Then</p>
                    <div className="w-56 space-y-2">
                      <label></label>
                      <Controller
                        control={control}
                        name={`criterias.${criteriaIndex}.options.${optionIndex}.value`}
                        render={({ field: { value, onChange } }) => (
                          <Select
                            defaultValue={optionValues[optionIndex]?.value}
                            value={value as string}
                            onValueChange={onChange}
                          >
                            <SelectTrigger
                              invalid={!!errors.criterias?.[criteriaIndex]?.options?.[optionIndex]?.value?.message}
                            >
                              <SelectValue placeholder="Select an option" className="placeholder:text-primary" />
                            </SelectTrigger>
                            <SelectContent>
                              {optionValues.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                    <div className="space-y-2">
                      <label></label>
                      <Button
                        type="button"
                        variant="text"
                        color="light-blue"
                        size="iconSmall"
                        trailingIcon={<Delete className="fill-support-blue" />}
                        onClick={() => handleRemoveSelectOption(optionIndex)}
                      ></Button>
                    </div>
                  </div>
                  {errors.criterias?.[criteriaIndex]?.options?.[optionIndex]?.option_value?.message && (
                    <p className="text-error text-sm">
                      {errors.criterias?.[criteriaIndex]?.options?.[optionIndex]?.option_value?.message}
                    </p>
                  )}
                  {errors.criterias?.[criteriaIndex]?.options?.[optionIndex]?.option_to_value?.message && (
                    <p className="text-error text-sm">
                      {errors.criterias?.[criteriaIndex]?.options?.[optionIndex]?.option_to_value?.message}
                    </p>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="text"
                color="light-blue"
                size="small"
                trailingIcon={<Add />}
                onClick={handleAddSelectOption}
              >
                Add option
              </Button>
              {(errors.criterias?.[criteriaIndex]?.options?.root?.message ||
                errors.criterias?.[criteriaIndex]?.options?.message) && (
                <p className="text-error text-sm">
                  {errors.criterias?.[criteriaIndex]?.options?.root?.message ||
                    errors.criterias?.[criteriaIndex]?.options?.message}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
      {criteria.mode === "COMMITMENT" && (
        <div className="space-y-6">
          <div className="grid grid-cols-3 gap-6">
            <div className="col-span-2">
              <Input
                placeholder="Question title"
                {...register(`criterias.${criteriaIndex}.title`)}
                variant={errors.criterias?.[criteriaIndex]?.title ? "error" : "default"}
                errorMessage={errors.criterias?.[criteriaIndex]?.title?.message}
                maxLength={CHARACTER_COUNTER_DEFAULTS.MAX_TITLE_LENGTH}
                aria-describedby="question-title-character-counter"
              />
              <CharacterCounter
                className="mt-2"
                id="question-title-character-counter"
                value={titleTextValue?.length || 0}
                max={CHARACTER_COUNTER_DEFAULTS.MAX_TITLE_LENGTH}
              />
            </div>
            <div className="col-span-1">
              <div className="space-y-2">
                <label></label>
                <Controller
                  control={control}
                  name={`criterias.${criteriaIndex}.input_type`}
                  render={({ field: { value, onChange } }) => (
                    <Select
                      defaultValue={CRITERIA_INPUT_TYPES[0].value}
                      value={value as string}
                      onValueChange={(v) => {
                        handleInputTypeChange(v);
                        onChange(v);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {CRITERIA_INPUT_TYPES.map((inputType) => (
                          <SelectItem key={inputType.value} value={inputType.value}>
                            {inputType.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
          </div>
          <Textarea
            aria-describedby="help-text-character-counter"
            label="Help text (optional)"
            placeholder="Enter help text"
            maxLength={CHARACTER_COUNTER_DEFAULTS.MAX_HELP_TEXT_LENGTH}
            rows={2}
            {...register(`criterias.${criteriaIndex}.help_text`)}
            errorMessage={errors.criterias?.[criteriaIndex]?.help_text?.message}
          />
          <CharacterCounter
            className="mt-2"
            id="help-text-character-counter"
            value={helpTextValue?.length || 0}
            max={CHARACTER_COUNTER_DEFAULTS.MAX_HELP_TEXT_LENGTH}
          />
          <div className="my-6 space-y-8">
            {inputType === "YES_NO" && (
              <>
                <div className="flex items-center gap-4">
                  <div className="w-32 h-12 bg-tonal-dark-cream-96 rounded-lg text-primary px-3 py-4">Yes</div>
                  <p className="text-tonal-dark-cream-20">Then</p>
                  <div className="w-56">
                    <Controller
                      control={control}
                      name={`criterias.${criteriaIndex}.options.0.value`}
                      render={({ field: { value, onChange } }) => (
                        <Select defaultValue={optionValues[0]?.value} value={value as string} onValueChange={onChange}>
                          <SelectTrigger invalid={!!errors.criterias?.[criteriaIndex]?.options?.[0]?.value?.message}>
                            <SelectValue placeholder="Select an option" className="placeholder:text-primary" />
                          </SelectTrigger>
                          <SelectContent>
                            {optionValues.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {capitalizeFirstLetter(option.label.toLocaleLowerCase())}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-32 h-12 bg-tonal-dark-cream-96 rounded-lg text-primary px-3 py-4">No</div>
                  <p className="text-tonal-dark-cream-20">Then</p>
                  <div className="w-56">
                    <Controller
                      control={control}
                      name={`criterias.${criteriaIndex}.options.1.value`}
                      render={({ field: { value, onChange } }) => (
                        <Select defaultValue={optionValues[1]?.value} value={value as string} onValueChange={onChange}>
                          <SelectTrigger invalid={!!errors.criterias?.[criteriaIndex]?.options?.[1]?.value?.message}>
                            <SelectValue placeholder="Select an option" className="placeholder:text-primary" />
                          </SelectTrigger>
                          <SelectContent>
                            {optionValues.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {capitalizeFirstLetter(option.label.toLocaleLowerCase())}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                  </div>
                </div>
              </>
            )}
            {inputType === "SELECT" && (
              <div className="space-y-5">
                {options.map((_, optionIndex) => (
                  <div key={optionIndex} className="flex items-center gap-4">
                    <div className="w-32">
                      <Input
                        placeholder="Value"
                        {...register(`criterias.${criteriaIndex}.options.${optionIndex}.option_value`)}
                        variant={
                          errors.criterias?.[criteriaIndex]?.options?.[optionIndex]?.option_value ? "error" : "default"
                        }
                      />
                    </div>
                    <p className="text-tonal-dark-cream-20">Then</p>
                    <div className="w-56 space-y-2">
                      <label></label>
                      <Controller
                        control={control}
                        name={`criterias.${criteriaIndex}.options.${optionIndex}.value`}
                        render={({ field: { value, onChange } }) => (
                          <Select
                            defaultValue={optionValues[optionIndex]?.value}
                            value={value as string}
                            onValueChange={onChange}
                          >
                            <SelectTrigger
                              invalid={!!errors.criterias?.[criteriaIndex]?.options?.[optionIndex]?.value?.message}
                            >
                              <SelectValue placeholder="Select an option" className="placeholder:text-primary" />
                            </SelectTrigger>
                            <SelectContent>
                              {optionValues.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {/* TODO: Consider adding locale parameter: option.label.toLocaleLowerCase(locale || 'en') */}
                                  {capitalizeFirstLetter(option.label.toLocaleLowerCase())}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                    <div className="space-y-2">
                      <label></label>
                      <Button
                        type="button"
                        variant="text"
                        color="light-blue"
                        size="iconSmall"
                        trailingIcon={<Delete className="fill-support-blue" />}
                        onClick={() => handleRemoveSelectOption(optionIndex)}
                      ></Button>
                    </div>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="text"
                  color="light-blue"
                  size="small"
                  trailingIcon={<Add />}
                  onClick={handleAddSelectOption}
                >
                  Add option
                </Button>
                {(errors.criterias?.[criteriaIndex]?.options?.root?.message ||
                  errors.criterias?.[criteriaIndex]?.options?.message) && (
                  <p className="text-error text-sm">
                    {errors.criterias?.[criteriaIndex]?.options?.root?.message ||
                      errors.criterias?.[criteriaIndex]?.options?.message}
                  </p>
                )}
              </div>
            )}
          </div>
          {["REPORT_SET", "OTHER_COST"].includes(criteria.type) && (
            <p className="text-tonal-dark-cream-60 italic text-sm">
              Save the setup step form with the data required before creating the criterias for the &quot;Then&quot;
              values to appear.
            </p>
          )}
        </div>
      )}
    </div>
  );
}

export function CriteriaCardSkeleton() {
  return (
    <div className="bg-background p-6 rounded-[20px]">
      <Skeleton className="h-8 w-40 rounded-xl mb-4" />
      <div className="flex items-center justify-between py-2">
        <Skeleton className="h-6 w-48 rounded" />
        <Skeleton className="h-8 w-32 rounded" />
      </div>
      <div className="space-y-6 mt-4">
        <div className="grid grid-cols-3 gap-6">
          <Skeleton className="col-span-2 h-10 rounded" />
          <Skeleton className="col-span-1 h-10 rounded" />
        </div>
        <Skeleton className="h-20 rounded" />
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Skeleton className="w-32 h-12 rounded-lg" />
            <Skeleton className="w-8 h-4 rounded" />
            <Skeleton className="w-56 h-10 rounded" />
          </div>
          <div className="flex items-center gap-4">
            <Skeleton className="w-32 h-12 rounded-lg" />
            <Skeleton className="w-8 h-4 rounded" />
            <Skeleton className="w-56 h-10 rounded" />
          </div>
        </div>
      </div>
    </div>
  );
}
