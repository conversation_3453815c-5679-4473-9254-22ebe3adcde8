import { useEffect } from "react";

/**
 * Custom hook for handling unsaved changes detection and browser navigation protection
 * Based on React Hook Form
 *
 * @param isDirty - The isDirty state from React Hook Form's formState
 * @returns hasUnsavedChanges - Boolean indicating if there are unsaved changes
 */
export function useUnsavedChanges(isDirty: boolean) {
  // Handle browser navigation (back button, refresh, close tab)
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isDirty) {
        event.preventDefault();
        event.returnValue = "";
        return "";
      }
    };

    // Only add listener when there are unsaved changes
    if (isDirty) {
      window.addEventListener("beforeunload", handleBeforeUnload);
    }

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [isDirty]);

  return isDirty;
}
