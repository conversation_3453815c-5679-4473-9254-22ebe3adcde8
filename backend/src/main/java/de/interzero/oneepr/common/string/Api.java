package de.interzero.oneepr.common.string;

public class Api {

    private Api() {
        throw new IllegalStateException("Utility class");
    }

    // Customer API
    public static final String CUSTOMER_API = "/customer";

    public static final String CONSENT = CUSTOMER_API + "/consent";

    public static final String CUSTOMER_CONSENT = CUSTOMER_API + "/customer-consent";

    public static final String CLUSTER = CUSTOMER_API + "/cluster";

    public static final String CUSTOMER_PHONE = CUSTOMER_API + "/customer-phone";

    public static final String COMPANY_EMAIL = CUSTOMER_API + "/CompanyEmail";

    public static final String REASONS = CUSTOMER_API + "/reasons";

    public static final String RECOMMEND_COUNTRY = CUSTOMER_API + "/RecommendCountry";

    public static final String CUSTOMER_DOCUMENT = CUSTOMER_API + "/customer-document";

    public static final String THIRD_PARTY_INVOICES = CUSTOMER_API + "/third-party-invoices";

    public static final String CUSTOMER_INVITE_TOKEN = CUSTOMER_API + "/customer-invite-token";

    public static final String CUSTOMER_ACTIVITY = CUSTOMER_API + "/customer-activity";

    public static final String SERVICE_NEXT_STEPS = CUSTOMER_API + "/service-next-steps";

    public static final String CUSTOMER_COMMITMENTS = CUSTOMER_API + "/customer-commitments";

    public static final String COUNTRY_PRICE_LISTS = CUSTOMER_API + "/country-price-lists";

    public static final String REPRESENTATIVE_TIERS = CUSTOMER_API + "/representative-tiers";

    public static final String OTHER_COSTS = CUSTOMER_API + "/other-costs";

    public static final String TERMINATION = CUSTOMER_API + "/terminations";

    public static final String REQUIRED_INFORMATIONS = CUSTOMER_API + "/required-informations";

    public static final String INVITE = CUSTOMER_API + "/invite";

    public static final String COUPON = CUSTOMER_API + "/coupon";

    public static final String FILES = CUSTOMER_API + "/files";

    public static final String COMMISSION = CUSTOMER_API + "/commission";

    public static final String CUSTOMERS = CUSTOMER_API + "/customers";

    public static final String LICENSE_VOLUME_REPORT_ITEM = CUSTOMER_API + "/license-volume-report-item";

    public static final String LICENSE_VOLUME_REPORT = CUSTOMER_API + "/license-volume-report";

    public static final String COMPANY = CUSTOMER_API + "/company";

    public static final String CUSTOMER_PACKAGING_SERVICES = CUSTOMER_API + "/packaging-services";

    public static final String SHOPPING_CART = CUSTOMER_API + "/shopping-cart";

    public static final String CERTIFICATES = CUSTOMER_API + "/certificates";

    public static final String LICENSES = CUSTOMER_API + "/licenses";

    // Admin API
    public static final String ADMIN_API = "/admin";

    public static final String CRITERIAS = ADMIN_API + "/criterias";

    public static final String FRACTION_ICONS = ADMIN_API + "/fraction-icons";

    public static final String ADMIN_OTHER_COSTS = ADMIN_API + "/other-costs";

    public static final String PACKAGING_SERVICES = ADMIN_API + "/packaging-services";

    public static final String PRICE_LISTS = ADMIN_API + "/price-lists";

    public static final String REPORT_SET_FRACTIONS = "/report-set-fractions";

    public static final String REPORT_SET_COLUMN_FRACTIONS = ADMIN_API + "report-set-column-fractions";

    public static final String REPORT_SET_COLUMNS = ADMIN_API + "/report-set-columns";

    public static final String REPORT_SET_PRICE_LISTS = ADMIN_API + "/report-set-price-lists";

    public static final String REPORT_SET_FREQUENCIES = ADMIN_API + "/report-set-frequencies";

    public static final String ADMIN_REQUIRED_INFORMATIONS = ADMIN_API + "/required-informations";

    public static final String ADMIN_REPRESENTATIVE_TIERS = ADMIN_API + "/representative-tiers";

    public static final String SETTINGS = ADMIN_API + "/settings";

    public static final String COUNTRIES = ADMIN_API + "/countries";

    public static final String REPORT_SETS = ADMIN_API + "/report-sets";

    public static final String SERVICE_SETUPS = ADMIN_API + "/service-setups";

    public static final String UPLOAD_FILES = ADMIN_API + "/upload-files";

    public static final String UPLOAD_DATA = ADMIN_API + "/upload-data";

    public static final String ADMIN = ADMIN_API + "/admin";

    // Auth API
    public static final String AUTH_API = "/auth";

    public static final String AUTH = AUTH_API + "/auth";

    public static final String USER = AUTH_API + "/user";


}
