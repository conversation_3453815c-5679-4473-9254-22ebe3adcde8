package de.interzero.oneepr.customer.integration;

import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.customer.dto.CreateCustomerDto;
import de.interzero.oneepr.customer.integration.dto.DirectLicenseContractsDto;
import jakarta.validation.constraints.NotNull;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Service
public class MondayService {

    public void sendDataBoardCustomer(CreateCustomerDto data,
                                      Integer id) {
    }

    public void updateCustomerEmailMonday(Map<String,? extends Serializable> email) {
    }

    public void updateBoardAccountsMonday(Company updatedCompany,
                                          Integer integer) {
    }

    public void createLicenseObligation(String country,
                                        String email,
                                        String customerId,
                                        List<String> licenseType) {
        // TODO implement this createLicenseObligation
    }

    public Integer createLicenseServiceContract(LicenseServiceContract dto,
                                                Integer customerId) {
        // TODO implement this createLicenseServiceContract
        return customerId;
    }

    public void createOpportunity(String customerId,
                                  String customerName,
                                  List<String> licenseServiceContractIds) {
        // TODO implement this createOpportunity
    }

    public Integer createDirectLicensingContract(DirectLicenseContractsDto dto) {
        // TODO implement this createDirectLicensingContract
        return null;
    }


    public void removeItem(Integer id,
                           @NotNull String countryName) {
        // TODO implement this removeItem
    }
}
