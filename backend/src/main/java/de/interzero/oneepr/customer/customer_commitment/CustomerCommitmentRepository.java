package de.interzero.oneepr.customer.customer_commitment;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA repository for the {@link CustomerCommitment} entity.
 */
@Repository
public interface CustomerCommitmentRepository extends JpaRepository<CustomerCommitment, Integer> {

    /**
     * Finds a single non-deleted CustomerCommitment by its ID.
     *
     * @param id The ID of the CustomerCommitment.
     * @return an Optional containing the found CustomerCommitment, or empty if not found.
     */
    @EntityGraph(attributePaths = {"customer"})
    Optional<CustomerCommitment> findByIdAndDeletedAtIsNull(Integer id);

    Optional<CustomerCommitment> findByCustomerEmailAndCountryCodeAndYear(String customerEmail,
                                                                          String countryCode,
                                                                          Integer year);

    /**
     * Finds all non-deleted CustomerCommitments for a given customer email and year, across all country codes.
     *
     * @param customerEmail The email of the customer.
     * @param year          The year of the commitment.
     * @return a List of found CustomerCommitments.
     */
    List<CustomerCommitment> findAllByCustomerEmailAndYearAndDeletedAtIsNull(String customerEmail,
                                                                             Integer year);

    /**
     * Finds all non-deleted CustomerCommitments for a given customer email, year, and specific country code.
     *
     * @param customerEmail The email of the customer.
     * @param year          The year of the commitment.
     * @param countryCode   The country code.
     * @return a List of found CustomerCommitments.
     */
    List<CustomerCommitment> findAllByCustomerEmailAndYearAndCountryCodeAndDeletedAtIsNull(String customerEmail,
                                                                                           Integer year,
                                                                                           String countryCode);

    /**
     * <p>Finds customer commitments by customer email and country codes, ignoring deleted ones.</p>
     *
     * @param email        the email of the customer
     * @param countryCodes the list of country codes
     * @return list of matching customer commitments
     */
    List<CustomerCommitment> findByCustomerEmailAndCountryCodeInAndDeletedAtIsNull(String email,
                                                                                   List<String> countryCodes);

}
