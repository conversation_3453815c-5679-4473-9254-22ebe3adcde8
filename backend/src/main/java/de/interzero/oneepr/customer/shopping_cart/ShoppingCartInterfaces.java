package de.interzero.oneepr.customer.shopping_cart;

import lombok.Data;

import java.util.List;
import java.util.Map;

public class ShoppingCartInterfaces {

    private ShoppingCartInterfaces() {
    }

    @Data
    public static class Option {

        private String label;

        private String value;
    }

    @Data
    public static class Question {

        private String id;

        private String mode;

        private String packagingId;

        private String type;

        private String title;

        private String helpText;

        private String inputType;

        private List<Option> options;

        private String answer;
    }

    @Data
    public static class Commitment {

        private boolean filled;

        private Map<String, Question> questions;
    }

    @Data
    public static class Fraction {

        private String key;

        private String name;

        private double multiplier;

        private String value;
    }

    @Data
    public static class ItemChild {

        private int id;

        private String name;

        private String description;

        private double price;

        private int level;
    }

    @Data
    public static class Item {

        private int id;

        private String name;

        private String description;

        private double price;

        private List<ItemChild> children;
    }

    @Data
    public static class ReportSet {

        private String id;

        private String type;

        private String name;

        private String mode;

        private String file;

        private List<Item> items;
    }

    @Data
    public static class PackageDto {

        private String id;

        private String title;

        private boolean added;

        private boolean required;

        private double price;

        private String service;

        private ReportSet reportSet;

        private Map<String, Fraction> fractions;

        private double fractionsCost;
    }

    @Data
    public static class ConditionType {

        private String condition;

        private String value;
    }

    @Data
    public static class PriceListDto {

        private String id;

        private String title;

        private String serviceType;

        private String description;

        private ConditionType conditionType;

        private String startDate;

        private String endDate;

        private Double price;

        private double basePrice;

        private double minPrice;

        private double registrationFee;

        private double handlingFee;

        private double variableHandlingFee;
    }

    @Data
    public static class RepresentativeTier {

        private String id;

        private String name;

        private double price;
    }

    @Data
    public static class LicenseDto {

        private String type;

        private String year;

        private Commitment commitment;

        private Map<String, PackageDto> packagingServices;

        private PriceListDto priceList;

        private RepresentativeTier representativeTier;
    }

    @Data
    public static class CountryDto {

        private int id;

        private String code;

        private String name;

        private String flag;
    }

    @Data
    public static class ShopData {

        private CountryDto country;

        private LicenseDto license;

        private Map<String, Object> actionGuide;
    }

    @Data
    public static class ShopItem {

        private Map<String, ShopData> items;
    }
}
