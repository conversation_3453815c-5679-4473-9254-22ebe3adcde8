package de.interzero.oneepr.customer.shopping_cart.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @ts-legacy This DTO comes from TypeScript and needs review.
 */
@Schema(description = "Fields required to update a shopping cart")
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateShoppingCartDto extends BaseDto {

    @JsonProperty("email")
    @Schema(
            description = "Shopping cart email",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String email;

    @JsonProperty("journey")
    @Schema(
            description = "Shopping cart journey",
            example = "DIRECT_LICENSE",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED,
            implementation = ShoppingCart.Journey.class
    )
    private ShoppingCart.Journey journey;

    @JsonProperty("journey_step")
    @Schema(
            description = "Shopping cart journey step",
            example = "SHOPPING_CART",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String journeyStep;

    @JsonProperty("vat_percentage")
    @Schema(
            description = "Shopping cart vat percentage",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer vatPercentage;

    @JsonProperty("items")
    @Schema(
            description = "Shopping cart items",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private List<ItemDto> items;

    @JsonProperty("payment")
    @Schema(
            description = "Shopping cart payment details",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private PaymentDto payment;

    @JsonProperty("coupon")
    @Schema(
            description = "Shopping cart coupon",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String coupon;

    @JsonProperty("coupon_type")
    @Schema(
            description = "Shopping cart coupon type",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private ShoppingCart.CouponType couponType;

    @JsonProperty("coupon_url_link")
    @Schema(
            description = "Shopping cart coupon url link",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String couponUrlLink;

    @JsonProperty("affiliate_link")
    @Schema(
            description = "Shopping cart affiliate link",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String affiliateLink;

    @JsonProperty("affiliate_type")
    @Schema(
            description = "Shopping cart affiliate type",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private ShoppingCart.TypeAffiliate affiliateType;

    @JsonProperty("affiliate_customer_id")
    @Schema(
            description = "Shopping cart affiliate customer id",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer affiliateCustomerId;

    @JsonProperty("affiliate_partner_id")
    @Schema(
            description = "Shopping cart affiliate partner id",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer affiliatePartnerId;


}
