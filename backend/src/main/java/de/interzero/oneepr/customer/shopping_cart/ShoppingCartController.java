package de.interzero.oneepr.customer.shopping_cart;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.shopping_cart.dto.CreateShoppingCartDto;
import de.interzero.oneepr.customer.shopping_cart.dto.CreateShoppingCartItemDto;
import de.interzero.oneepr.customer.shopping_cart.dto.UpdateShoppingCartDto;
import de.interzero.oneepr.customer.shopping_cart.dto.UpdateShoppingCartItemDto;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import static de.interzero.oneepr.common.string.Role.*;

@RestController
@RequestMapping(Api.SHOPPING_CART)
@RequiredArgsConstructor
public class ShoppingCartController {

    private final ShoppingCartService shoppingCartService;

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ShoppingCart create(@RequestBody CreateShoppingCartDto body) {
        return shoppingCartService.create(body);
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @GetMapping("/{id}")
    public ShoppingCart findOne(@PathVariable String id) {
        return shoppingCartService.findOne(id);
    }


    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/email/{email}")
    public ShoppingCart findOneByEmail(@PathVariable String email) {
        return shoppingCartService.findOneByEmail(email, AuthUtil.getRelevantUserDetails());
    }


    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/purchased/{email}")
    public ShoppingCart findLastPurchasedByEmail(@PathVariable String email) {
        return shoppingCartService.findLastPurchasedByEmail(email, AuthUtil.getRelevantUserDetails());
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @PutMapping("/{id}")
    public ShoppingCart update(@PathVariable String id,
                               @RequestBody UpdateShoppingCartDto body) {
        return shoppingCartService.update(id, body, AuthUtil.getRelevantUserDetails());
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @PostMapping("/{id}/items")
    @ResponseStatus(HttpStatus.CREATED)
    public ShoppingCart addItem(@PathVariable String id,
                                @RequestBody CreateShoppingCartItemDto body) {
        return shoppingCartService.addItem(id, body, AuthUtil.getRelevantUserDetails());
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @PutMapping("/{id}/items/{item_id}")
    public ShoppingCart updateItem(@PathVariable String id,
                                   @PathVariable("item_id") Integer itemId,
                                   @RequestBody UpdateShoppingCartItemDto body) {
        return shoppingCartService.updateItem(id, itemId, body, AuthUtil.getRelevantUserDetails());
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @DeleteMapping("/{id}/items/{item_id}")
    public ShoppingCart removeItem(@PathVariable String id,
                                   @PathVariable("item_id") Integer itemId) {
        return shoppingCartService.removeItem(id, itemId, AuthUtil.getRelevantUserDetails());
    }
}
