package de.interzero.oneepr.customer.partner;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.entity.CouponPartner;
import de.interzero.oneepr.customer.entity.MarketingMaterialPartner;
import de.interzero.oneepr.customer.entity.PartnerBanking;
import de.interzero.oneepr.customer.entity.PartnerContract;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "partner",
        schema = "public"
)
public class Partner {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "partner_id_gen"
    )
    @SequenceGenerator(
            name = "partner_id_gen",
            sequenceName = "partner_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "first_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("first_name")
    private String firstName;

    @NotNull
    @Column(
            name = "last_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("last_name")
    private String lastName;

    @NotNull
    @Column(
            name = "email",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("email")
    private String email;

    @NotNull
    @Column(
            name = "user_id",
            nullable = false
    )
    @JsonProperty("user_id")
    private Integer userId;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @Column(
            name = "commission_mode",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("commission_mode")
    private String commissionMode;

    @Column(name = "commission_percentage")
    @JsonProperty("commission_percentage")
    private Integer commissionPercentage;

    @Column(name = "no_provision_negotiated")
    @JsonProperty("no_provision_negotiated")
    private Boolean noProvisionNegotiated;

    @Column(
            name = "payout_cycle",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("payout_cycle")
    private String payoutCycle;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @JsonProperty("status")
    private Status status = Status.NO_UPDATES;

    @Column(
            name = "checked",
            nullable = false
    )
    @JsonProperty("checked")
    private Boolean checked = false;

    @OneToMany(
            mappedBy = "partner",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL
    )
    @JsonProperty("companies")
    @JsonIgnore
    private List<Company> companies;

    @OneToMany(
            mappedBy = "partner",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL
    )
    @JsonProperty("coupon")
    @JsonIgnore
    private List<CouponPartner> coupons;

    @OneToMany(
            mappedBy = "partner",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL
    )
    @JsonProperty("marketing_material_partners")
    @JsonIgnore
    private List<MarketingMaterialPartner> marketingMaterialPartners;

    @OneToOne(
            mappedBy = "partner",
            cascade = CascadeType.ALL
    )
    @JsonProperty("partner_banking")
    @JsonIgnore
    private PartnerBanking partnerBanking;

    @OneToOne(
            mappedBy = "partner",
            cascade = CascadeType.ALL
    )
    @JsonProperty("partner_contract")
    @JsonIgnore
    private PartnerContract partnerContract;

    @OneToMany(
            mappedBy = "affiliatePartner",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL
    )
    @JsonProperty("affiliate_shopping_cart")
    @JsonIgnore
    private List<ShoppingCart> affiliateShoppingCart;

    @PrePersist
    protected void onCreate() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

    public enum Status {
        NO_UPDATES,
        IMPROVED_CONTRACT,
        DENIED_CONTRACT,
        REQUESTED_COMMISSION,
        CHANGED_INFORMATION
    }
}
