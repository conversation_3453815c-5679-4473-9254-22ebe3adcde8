package de.interzero.oneepr.admin.service_setup;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.country_price_list.CountryPriceList;
import de.interzero.oneepr.admin.country_price_list.CountryPriceListRepository;
import de.interzero.oneepr.admin.criteria.Criteria;
import de.interzero.oneepr.admin.criteria.CriteriaOption;
import de.interzero.oneepr.admin.criteria.CriteriaRepository;
import de.interzero.oneepr.admin.other_cost.OtherCost;
import de.interzero.oneepr.admin.other_cost.OtherCostRepository;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.price_list.PriceList;
import de.interzero.oneepr.admin.price_list.ReportSetPriceListItem;
import de.interzero.oneepr.admin.report_set.ReportSet;
import de.interzero.oneepr.admin.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.report_set.ReportSetService;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumnRepository;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFractionRepository;
import de.interzero.oneepr.admin.report_set_frequency.ReportSetFrequency;
import de.interzero.oneepr.admin.report_set_frequency.ReportSetFrequencyRepository;
import de.interzero.oneepr.admin.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.admin.report_set_price_list.ReportSetPriceListRepository;
import de.interzero.oneepr.admin.representative_tier.RepresentativeTier;
import de.interzero.oneepr.admin.representative_tier.RepresentativeTierRepository;
import de.interzero.oneepr.admin.required_information.RequiredInformation;
import de.interzero.oneepr.admin.required_information.RequiredInformationRepository;
import de.interzero.oneepr.admin.service_setup.dto.*;
import de.interzero.oneepr.common.util.JsonUtil;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;
import de.interzero.oneepr.admin.report_set.dto.ReportSetDetailDto;
import java.time.Year;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@SuppressWarnings("java:S6539")
public class ServiceSetupService {

    private final CountryRepository countryRepository;

    private final PackagingServiceRepository packagingServiceRepository;

    private final ReportSetFrequencyRepository reportSetFrequencyRepository;

    private final ReportSetRepository reportSetRepository;

    private final ReportSetFractionRepository reportSetFractionRepository;

    private final ReportSetColumnRepository reportSetColumnRepository;

    private final ReportSetPriceListRepository reportSetPriceListRepository;

    private final CountryPriceListRepository countryPriceListRepository;

    private final RepresentativeTierRepository representativeTierRepository;

    private final RequiredInformationRepository requiredInformationRepository;

    private final OtherCostRepository otherCostRepository;

    private final ServiceSetupMapper serviceSetupMapper;

    private final CriteriaRepository criteriaRepository;

    private final ObjectMapper objectMapper;

    private final ModelMapper modelMapper;

    private final ReportSetMapper reportSetMapper;

    private final ReportSetService reportSetService;
    private static final String OBLIGED = "OBLIGED";

    private static final String COUNTRY_NOT_FOUND = "Country not found";

    @Transactional(readOnly = true)
    public ServiceSetupDto findServiceSetup(String countryCode) {
        Country country = countryRepository.findByCode(countryCode)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));
        populateCountryRelations(country);
        return serviceSetupMapper.toServiceSetupDto(country);
    }

    /**
     * Orchestrates the loading of all hierarchical data related to a given {@link Country} entity.
     * <p>
     * <b>Why it's needed:</b> JPA lazy-loading is not sufficient for building the complex, multi-level DTO
     * required by the API. This method explicitly fetches all necessary data in an optimized way to prevent
     * the "N+1 query problem," where fetching a list of items results in N additional queries to fetch their children.
     * <p>
     * <b>What it does:</b> It systematically fetches data level by level, starting from the direct children of the
     * country and moving deeper into the hierarchy (PackagingService -> ReportSet -> Fractions/Columns). For deeply nested
     * tree structures like Fractions and Columns, it uses an efficient in-memory stitching strategy:
     * <ol>
     *     <li>Fetch all descendants in a single database query.</li>
     *     <li>Build a lookup map for quick access to any item by its ID.</li>
     *     <li>Iterate through the descendants, attaching each child to its parent within the map.</li>
     *     <li>Filter for the top-level (root) nodes, which now contain their fully-built child trees.</li>
     *     <li>Attach these completed trees to their corresponding {@link ReportSet} entities.</li>
     * </ol>
     * This process results in a fully hydrated {@link Country} entity graph, ready for mapping to a DTO.
     * All operations on managed collections use the <code>clear()</code> and <code>addAll()</code> pattern
     * to ensure compatibility with Hibernate's change tracking.
     *
     * @param country The managed JPA {@link Country} entity to be populated. Its collections will be modified in-place.
     */
    private void populateCountryRelations(Country country) {
        List<PackagingService> packagingServices = packagingServiceRepository.findByCountryAndDeletedAtIsNull(country);
        country.getPackagingServices().clear();
        country.getPackagingServices().addAll(packagingServices);

        country.getCountryPriceLists().clear();
        country.getCountryPriceLists().addAll(countryPriceListRepository.findByCountryAndDeletedAtIsNull(country));

        country.getRepresentativeTiers().clear();
        country.getRepresentativeTiers().addAll(representativeTierRepository.findByCountryAndDeletedAtIsNull(country));

        country.getRequiredInformations().clear();
        country.getRequiredInformations()
                .addAll(requiredInformationRepository.findByCountryAndDeletedAtIsNull(country));

        country.getOtherCosts().clear();
        country.getOtherCosts().addAll(otherCostRepository.findByCountryAndDeletedAtIsNull(country));

        if (packagingServices.isEmpty()) {
            return;
        }

        List<ReportSet> reportSets = reportSetRepository.findByPackagingServiceInAndDeletedAtIsNull(packagingServices);
        List<ReportSetFrequency> reportSetFrequencies = reportSetFrequencyRepository.findByPackagingServiceInAndDeletedAtIsNull(
                packagingServices);

        Map<Integer, List<ReportSet>> setsByServiceId = reportSets.stream()
                .collect(Collectors.groupingBy(ReportSet::getPackagingServiceId));
        Map<Integer, List<ReportSetFrequency>> freqsByServiceId = reportSetFrequencies.stream()
                .collect(Collectors.groupingBy(ReportSetFrequency::getPackagingServiceId));

        packagingServices.forEach(ps -> {
            ps.getReportSets().clear();
            ps.getReportSets().addAll(setsByServiceId.getOrDefault(ps.getId(), Collections.emptyList()));

            ps.getReportSetFrequencies().clear();
            ps.getReportSetFrequencies().addAll(freqsByServiceId.getOrDefault(ps.getId(), Collections.emptyList()));
        });

        if (reportSets.isEmpty()) {
            return;
        }
        List<ReportSetFraction> allFractions = reportSetFractionRepository.findByReportSetInAndDeletedAtIsNullOrderByLevelAscOrderAsc(
                reportSets);
        List<ReportSetColumn> allColumns = reportSetColumnRepository.findByReportSetInAndDeletedAtIsNullOrderByLevelAscOrderAsc(
                reportSets);
        List<ReportSetPriceList> priceLists = reportSetPriceListRepository.findByReportSetInAndDeletedAtIsNullOrderByCreatedAtAsc(
                reportSets);
        Map<Integer, ReportSetFraction> fractionMapById = allFractions.stream()
                .collect(Collectors.toMap(ReportSetFraction::getId, fraction -> fraction));

        Map<Integer, ReportSetColumn> columnMapById = allColumns.stream()
                .collect(Collectors.toMap(ReportSetColumn::getId, column -> column));

        allFractions.forEach(fraction -> {
            if (fraction.getParentId() != null) {
                ReportSetFraction parent = fractionMapById.get(fraction.getParentId());
                if (parent != null) {
                    parent.getChildren().add(fraction);
                }
            }
        });

        allColumns.forEach(column -> {
            if (column.getParentId() != null) {
                ReportSetColumn parent = columnMapById.get(column.getParentId());
                if (parent != null) {
                    parent.getChildren().add(column);
                }
            }
        });

        List<ReportSetFraction> topLevelFractions = allFractions.stream().filter(f -> f.getParentId() == null).toList();

        List<ReportSetColumn> topLevelColumns = allColumns.stream().filter(c -> c.getParentId() == null).toList();
        Map<Integer, List<ReportSetFraction>> fractionsBySetId = topLevelFractions.stream()
                .collect(Collectors.groupingBy(ReportSetFraction::getReportSetId));

        Map<Integer, List<ReportSetColumn>> columnsBySetId = topLevelColumns.stream()
                .collect(Collectors.groupingBy(ReportSetColumn::getReportSetId));

        Map<Integer, List<ReportSetPriceList>> priceListsBySetId = priceLists.stream()
                .collect(Collectors.groupingBy(ReportSetPriceList::getReportSetId));
        reportSets.forEach(rs -> {
            rs.getFractions().clear();
            rs.getFractions().addAll(fractionsBySetId.getOrDefault(rs.getId(), Collections.emptyList()));

            rs.getColumns().clear();
            rs.getColumns().addAll(columnsBySetId.getOrDefault(rs.getId(), Collections.emptyList()));

            rs.getPriceLists().clear();
            rs.getPriceLists().addAll(priceListsBySetId.getOrDefault(rs.getId(), Collections.emptyList()));
        });

    }

    /**
     * @ts-legacy The original Prisma query fetches a complex, deeply nested and filtered graph of data in a single operation.
     * JPA does not have a direct equivalent for efficiently filtering collections within a larger graph fetch.
     * To replicate the logic faithfully, this translation adopts a multi-query approach. It first fetches the
     * primary Country entity. Then, it executes a series of separate, targeted queries against different repositories
     * to fetch each required collection with its specific filters (e.g., `deletedAt IS NULL`).
     * Simple existence checks are performed using efficient `countBy...` repository methods, while more complex
     * data validations require fetching the actual collections. This approach ensures logical equivalence with the
     * original code's data requirements.
     */
    @Transactional(readOnly = true)
    @SuppressWarnings({"java:S3776", "java:S6541"})
    public ServiceSetupStatusDto getServiceSetupStatus(String countryCode) {
        int currentYear = Year.now().getValue();

        Country country = countryRepository.findByCode(countryCode)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));

        List<PackagingService> packagingServices = packagingServiceRepository.findActiveByCountryId(country.getId());

        // Packaging services
        if (packagingServices.isEmpty()) {
            return ServiceSetupStatusDto.incomplete("Packaging services are empty");
        }

        // Report sets must exist for every packaging service
        if (packagingServices.stream()
                .anyMatch(ps -> reportSetRepository.countByPackagingService_IdAndDeletedAtIsNull(ps.getId()) == 0)) {
            return ServiceSetupStatusDto.incomplete("There are packaging services without report sets");
        }

        for (PackagingService ps : packagingServices) {
            List<ReportSet> reportSets = reportSetRepository.findByPackagingService_IdAndDeletedAtIsNull(ps.getId());
            if (reportSets.isEmpty()) { // Defensive check, covered above, but provides a more specific message.
                return ServiceSetupStatusDto.incomplete(String.format("The packaging service \"%s\" is without any report set",
                                                                      ps.getName()));
            }

            for (ReportSet rs : reportSets) {
                // Fractions checks
                if (reportSetFractionRepository.countByReportSet_IdAndDeletedAtIsNull(rs.getId()) == 0) {
                    return ServiceSetupStatusDto.incomplete(String.format(
                            "The report set \"%s\" of packaging service \"%s\" is missing fractions",
                            rs.getName(),
                            ps.getName()));
                }

                List<ReportSetFraction> fractions = reportSetFractionRepository.findActiveFractionTreeByReportSetId(rs.getId());
                if (fractions.stream()
                        .anyMatch(f -> f.getHasSecondLevel() && (f.getChildren() == null || f.getChildren()
                                .isEmpty()))) {
                    return ServiceSetupStatusDto.incomplete(String.format(
                            "The report set \"%s\" of packaging service \"%s\" is missing second level fractions",
                            rs.getName(),
                            ps.getName()));
                }
                if (fractions.stream()
                        .anyMatch(f1 -> f1.getHasThirdLevel() && (f1.getChildren() == null || f1.getChildren()
                                .stream()
                                .anyMatch(f2 -> f2.getChildren() == null || f2.getChildren().isEmpty())))) {
                    return ServiceSetupStatusDto.incomplete(String.format(
                            "The report set \"%s\" of packaging service \"%s\" is missing third level fractions",
                            rs.getName(),
                            ps.getName()));
                }

                // Columns check
                if (reportSetColumnRepository.countByReportSet_IdAndParentIsNullAndDeletedAtIsNull(rs.getId()) == 0) {
                    return ServiceSetupStatusDto.incomplete(String.format(
                            "The report set \"%s\" of packaging service \"%s\" is missing columns",
                            rs.getName(),
                            ps.getName()));
                }

                // Price lists checks
                List<ReportSetPriceList> priceLists = reportSetPriceListRepository.findByReportSet_IdAndDeletedAtIsNull(
                        rs.getId());
                if (priceLists.isEmpty()) {
                    return ServiceSetupStatusDto.incomplete(String.format(
                            "The report set \"%s\" of packaging service \"%s\" is missing price lists",
                            rs.getName(),
                            ps.getName()));
                }
                if (priceLists.stream().noneMatch(pl -> pl.getLicenseYear() == currentYear)) {
                    return ServiceSetupStatusDto.incomplete(String.format(
                            "The report set \"%s\" of packaging service \"%s\" is missing price list for the current year (%d)",
                            rs.getName(),
                            ps.getName(),
                            currentYear));
                }
            }
        }

        // Report frequencies & Commitment Criteria checks
        for (PackagingService ps : packagingServices) {
            List<ReportSetFrequency> frequencies = reportSetFrequencyRepository.findByPackagingService_IdAndDeletedAtIsNull(
                    ps.getId());
            if (frequencies.isEmpty()) {
                return ServiceSetupStatusDto.incomplete("There are packaging services without report frequencies");
            }

            List<Criteria> commitmentCriteria = criteriaRepository.findByPackagingService_IdAndModeAndDeletedAtIsNull(
                    ps.getId(),
                    Criteria.Mode.COMMITMENT);
            List<ReportSet> reportSets = reportSetRepository.findByPackagingService_IdAndDeletedAtIsNull(ps.getId());

            if (reportSets.size() > 1 && commitmentCriteria.stream()
                    .noneMatch(c -> c.getType() == Criteria.Type.REPORT_SET)) {
                return ServiceSetupStatusDto.incomplete(
                        "There are packaging services with multiple report sets without commitment criteria");
            }

            if (frequencies.size() > 1 && commitmentCriteria.stream()
                    .noneMatch(c -> c.getType() == Criteria.Type.REPORT_FREQUENCY)) {
                return ServiceSetupStatusDto.incomplete(
                        "There are packaging services with multiple report frequencies without commitment criteria");
            }
        }

        // Country level price list check
        List<CountryPriceList> countryPriceLists = countryPriceListRepository.findByCountry_IdAndDeletedAtIsNull(country.getId());
        if (countryPriceLists.isEmpty()) {
            return ServiceSetupStatusDto.incomplete("There are no price lists selected");
        }
        String currentYearStr = String.valueOf(currentYear);
        if (countryPriceLists.stream()
                .noneMatch(cpl -> currentYearStr.equals(cpl.getPriceList().getConditionTypeValue()))) {
            return ServiceSetupStatusDto.incomplete(String.format(
                    "There is no price list for the current year (%d)",
                    currentYear));
        }

        return ServiceSetupStatusDto.completed();
    }

    /**
     * @ts-legacy The original Prisma query uses a filtered `include` to fetch packaging services and their relevant criteria in a single database call.
     * JPA and Hibernate do not directly support applying a WHERE clause to a JOIN FETCH'd collection in JPQL. To achieve an equivalent result,
     * this translation uses a two-query approach:
     * 1. The primary `PackagingService` entities are fetched based on the country code.
     * 2. A second, single query is executed to fetch all the filtered `Criteria` entities associated with the previously fetched packaging services.
     * The results are then combined in memory to build the final DTOs. This approach is efficient and maintains logical equivalence with the original query.
     */
    @Transactional(readOnly = true)
    public List<PackagingServiceSetupDto> findServiceSetupPackagingServices(String countryCode) {
        // Query 1: Fetch packaging services
        List<PackagingService> packagingServices = packagingServiceRepository.findByCountry_CodeAndDeletedAtIsNull(
                countryCode);

        if (packagingServices.isEmpty()) {
            return Collections.emptyList();
        }

        // Get IDs for the second query
        List<Integer> packagingServiceIds = packagingServices.stream().map(PackagingService::getId).toList();

        // Define filter parameters for criteria
        final Criteria.Mode mode = Criteria.Mode.COMMITMENT;
        final List<Criteria.Type> types = List.of(
                Criteria.Type.PACKAGING_SERVICE,
                Criteria.Type.REPORT_SET,
                Criteria.Type.REPORT_FREQUENCY);

        // Query 2: Fetch all relevant criteria in one go
        List<Criteria> allCriteria = criteriaRepository.findByPackagingService_IdInAndDeletedAtIsNullAndModeAndTypeIn(packagingServiceIds,
                                                                                                                      mode,
                                                                                                                      types);

        // Group criteria by their parent packaging service ID for efficient lookup
        Map<Integer, List<Criteria>> criteriaByServiceId = allCriteria.stream()
                .collect(Collectors.groupingBy(Criteria::getPackagingServiceId));

        // Map to DTO
        return packagingServices.stream().map(ps -> {
            List<Criteria> filteredCriteria = criteriaByServiceId.get(ps.getId());
            return PackagingServiceSetupDto.fromEntity(ps, filteredCriteria);
        }).toList();
    }

    /**
     * @ts-legacy The original Prisma query fetches `ReportSet` entities and includes their parent `PackagingService` along with a filtered list of `Criteria` on that parent.
     * JPA/Hibernate does not directly support applying a `WHERE` clause to a nested `JOIN FETCH` collection.
     * To replicate this logic, this translation uses a two-query approach:
     * 1. A single query fetches all active `ReportSet` entities for the given country, eagerly fetching their parent `PackagingService`.
     * 2. A second query fetches all `Criteria` entities that match the specific filters (type='REPORT_SET', mode='COMMITMENT') for the given country.
     * The results are then combined in memory by checking which `PackagingService` IDs from the first query are present in the list of `Criteria` from the second query.
     */
    @Transactional(readOnly = true)
    public List<ReportSetSetupDto> findServiceSetupReportSets(String countryCode) {
        // Query 1: Fetch ReportSets and their parent PackagingServices
        List<ReportSet> reportSets = reportSetRepository.findActiveByCountryCodeWithPackagingService(countryCode);

        if (reportSets.isEmpty()) {
            return Collections.emptyList();
        }

        // Query 2: Fetch all criteria that match the filter for the entire country
        List<Criteria> commitmentCriteria = criteriaRepository.findCommitmentCriteriaByCountryAndType(
                countryCode,
                Criteria.Mode.COMMITMENT,
                Criteria.Type.REPORT_SET);

        // Create a Set of PackagingService IDs that have the relevant criteria for efficient lookup
        Set<Integer> serviceIdsWithCriteria = commitmentCriteria.stream()
                .map(Criteria::getPackagingServiceId)
                .collect(Collectors.toSet());

        // Map to DTO, checking for each ReportSet if its parent service has criteria
        return reportSets.stream().map(rs -> {
            boolean hasCriteria = serviceIdsWithCriteria.contains(rs.getPackagingService().getId());
            return reportSetMapper.toReportSetSetupDto(rs, hasCriteria);
        }).toList();
    }

    /**
     * @ts-legacy The original Prisma query fetches a single `ReportSet` with a deeply nested graph of related collections,
     * each with its own filters and ordering. A single JPQL query with multiple `JOIN FETCH` statements for collections
     * would result in a `MultipleBagFetchException`.
     * <p>
     * To replicate this faithfully, this translation employs a multi-query strategy:
     * 1. A primary query fetches the root `ReportSet` entity and its to-one associations (`packaging_service`, `sheet_file`).
     * 2. Separate, subsequent queries are executed to fetch each required to-many collection (`fractions`, `columns`, `price_lists`)
     * with their respective filters and eager-fetched children.
     * 3. A simple count query checks for the existence of the filtered `Criteria` on the parent packaging service.
     * 4. Finally, the service method manually assembles the complete object graph by setting the fetched collections
     * onto the root entity before wrapping it in the final DTO.
     */
    @Transactional(readOnly = true)
    public ReportSetDetailDto findServiceSetupReportSet(String countryCode,
                                                        Integer reportSetId) {
        // 1. Fetch the root entity
        ReportSet reportSet = reportSetRepository.findActiveByIdAndCountryCodeWithAssociations(reportSetId, countryCode)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Report set not found"));

        // 2. Fetch related collections and data
        boolean hasCriteria = criteriaRepository.countByPackagingService_IdAndDeletedAtIsNullAndTypeAndMode(
                reportSet.getPackagingService()
                        .getId(),
                Criteria.Type.REPORT_SET,
                Criteria.Mode.COMMITMENT) > 0;

        List<ReportSetFraction> fractions = reportSetFractionRepository.findTopLevelActiveFractionsWithChildrenByReportSetId(
                reportSetId);

        List<ReportSetColumn> columns = reportSetColumnRepository.findTopLevelActiveColumnsWithChildrenByReportSetId(
                reportSetId);

        List<ReportSetPriceList> priceLists = reportSetPriceListRepository.findByReportSet_IdAndDeletedAtIsNullOrderByCreatedAtAsc(
                reportSetId);

        // 3. Assemble the graph
        reportSet.setFractions(fractions);
        reportSet.setColumns(columns);
        reportSet.setPriceLists(priceLists);

        // 4. Create and return the DTO
        return reportSetService.convertToDetailDto(reportSet, hasCriteria);
    }


    /**
     * @ts-legacy This method translates a Prisma query that uses a filtered include.
     * The translation uses a two-query approach to achieve the same result:
     * 1. Fetch all active ReportSetFrequency entities for a given country, along with their associated PackagingService.
     * 2. Fetch all relevant Criteria entities for the retrieved PackagingServices in a single second query.
     * The results are then combined in memory to build the final response DTOs.
     */
    @Transactional(readOnly = true)
    public List<ReportSetFrequencyResponseDto> findServiceSetupReportFrequencies(String countryCode) {
        List<ReportSetFrequency> frequencies = reportSetFrequencyRepository.findByPackagingService_Country_CodeAndDeletedAtIsNullAndPackagingService_DeletedAtIsNull(
                countryCode);

        if (frequencies.isEmpty()) {
            return Collections.emptyList();
        }

        List<PackagingService> packagingServices = frequencies.stream()
                .map(ReportSetFrequency::getPackagingService)
                .distinct()
                .toList();

        List<Criteria> allCriteria = criteriaRepository.findReportFrequencyCommitmentCriteriaForServices(packagingServices,
                                                                                                         Criteria.Type.REPORT_FREQUENCY,
                                                                                                         Criteria.Mode.COMMITMENT);

        Map<Integer, List<Criteria>> criteriaByServiceId = allCriteria.stream()
                .collect(Collectors.groupingBy(c -> c.getPackagingService().getId()));

        return frequencies.stream().map(frequency -> {
            PackagingService service = frequency.getPackagingService();
            List<Criteria> relatedCriteria = criteriaByServiceId.getOrDefault(service.getId(), Collections.emptyList());

            List<CriteriaDto> criteriaDtos = relatedCriteria.stream()
                    .map(c -> modelMapper.map(c, CriteriaDto.class))
                    .toList();

            PackagingServiceWithCriteriaDto serviceDto = modelMapper.map(
                    service,
                    PackagingServiceWithCriteriaDto.class);
            serviceDto.setCriterias(criteriaDtos);
            serviceDto.setCountryId(service.getCountry().getId());

            return ReportSetFrequencyResponseDto.builder()
                    .id(frequency.getId())
                    .rhythm(frequency.getRhythm())
                    .frequency(JsonUtil.parseJsonString(frequency.getFrequency()))
                    .packagingService(serviceDto)
                    .hasCriteria(!relatedCriteria.isEmpty())
                    .createdAt(frequency.getCreatedAt())
                    .deletedAt(frequency.getDeletedAt())
                    .updatedAt(frequency.getUpdatedAt())
                    .packagingServiceId(frequency.getPackagingServiceId())
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * @ts-legacy This method translates a Prisma query that uses a nested, filtered include.
     * The translation uses a two-query approach:
     * 1. Fetch all active RepresentativeTier entities for a given country.
     * 2. Fetch all relevant 'REPRESENTATIVE_TIER' commitment Criteria for that same country.
     * The results are then combined in memory. A single 'has_criteria' flag is calculated
     * and applied to all returned tiers, and the filtered criteria list is attached to the
     * nested country object, mirroring the original data structure.
     */
    @Transactional(readOnly = true)
    public List<RepresentativeTierResponseDto> findServiceSetupRepresentativeTiers(String countryCode) {
        List<RepresentativeTier> representativeTiers = representativeTierRepository.findAllByCountryCodeAndDeletedAtIsNullWithCountry(
                countryCode);

        if (representativeTiers.isEmpty()) {
            return Collections.emptyList();
        }

        List<Criteria> commitmentCriteria = criteriaRepository.findCommitmentCriteriaByCountryCode(
                countryCode,
                Criteria.Type.REPRESENTATIVE_TIER,
                Criteria.Mode.COMMITMENT);

        boolean hasCriteria = !commitmentCriteria.isEmpty();

        List<CriteriaDto> criteriaDtos = commitmentCriteria.stream()
                .map(c -> modelMapper.map(c, CriteriaDto.class))
                .toList();

        Country country = representativeTiers.getFirst().getCountry();
        CountryWithCriteriaDto countryDto = modelMapper.map(country, CountryWithCriteriaDto.class);
        countryDto.setCriterias(criteriaDtos);

        return representativeTiers.stream().map(tier -> {
            RepresentativeTierResponseDto tierDto = modelMapper.map(tier, RepresentativeTierResponseDto.class);
            tierDto.setCountry(countryDto);
            tierDto.setHasCriteria(hasCriteria);
            tierDto.setCountryId(tier.getCountry().getId());
            return tierDto;
        }).toList();
    }

    /**
     * @ts-legacy This method translates a Prisma query that uses a nested, filtered include.
     * The translation uses a two-query approach:
     * 1. Fetch all active OtherCost entities for a given country.
     * 2. Fetch all relevant 'OTHER_COST' commitment Criteria for that same country.
     * The results are then combined in memory. A single 'has_criteria' flag is calculated
     * and applied to all returned costs, and the filtered criteria list is attached to the
     * nested country object, mirroring the original data structure.
     */
    @Transactional(readOnly = true)
    public List<OtherCostResponseDto> findServiceSetupOtherCosts(String countryCode) {
        List<OtherCost> otherCosts = otherCostRepository.findByCountry_CodeAndDeletedAtIsNull(countryCode);

        if (otherCosts.isEmpty()) {
            return Collections.emptyList();
        }

        List<Criteria> commitmentCriteria = criteriaRepository.findCommitmentCriteriaByCountryCode(
                countryCode,
                Criteria.Type.OTHER_COST,
                Criteria.Mode.COMMITMENT);

        boolean hasCriteria = !commitmentCriteria.isEmpty();

        List<CriteriaDto> criteriaDtos = commitmentCriteria.stream()
                .map(c -> modelMapper.map(c, CriteriaDto.class))
                .toList();

        Country country = otherCosts.getFirst().getCountry();
        CountryWithCriteriaDto countryDto = modelMapper.map(country, CountryWithCriteriaDto.class);
        countryDto.setCriterias(criteriaDtos);

        return otherCosts.stream().map(cost -> {
            OtherCostResponseDto costDto = modelMapper.map(cost, OtherCostResponseDto.class);
            costDto.setCountry(countryDto);
            costDto.setHasCriteria(hasCriteria);
            costDto.setCountryId(cost.getCountry().getId());
            return costDto;
        }).toList();
    }

    @Transactional(readOnly = true)
    public List<CountryPriceListResponseDto> findServiceSetupPriceLists(String countryCode) {
        List<CountryPriceList> countryPriceLists = countryPriceListRepository.findActiveByCountryCodeWithPriceList(
                countryCode);

        return countryPriceLists.stream().map(cpl -> {
            CountryPriceListResponseDto dto = new CountryPriceListResponseDto();
            dto.setId(cpl.getId());
            dto.setCountryId(cpl.getCountry().getId());
            dto.setPriceListId(cpl.getPriceList().getId());
            dto.setPriceList(modelMapper.map(cpl.getPriceList(), PriceListDto.class));
            return dto;
        }).toList();
    }

    /**
     * @ts-legacy This method translates a Prisma query that uses a filtered include.
     * The translation uses a two-query approach to achieve the same result:
     * 1. Fetch all active RequiredInformation entities for a given country, along with their associated File.
     * 2. Fetch all relevant 'REQUIRED_INFORMATION' commitment Criteria for the retrieved entities.
     * The results are then combined in memory to build the final response DTOs, where each
     * DTO contains its specific list of criteria and a 'has_criteria' flag.
     */
    @Transactional(readOnly = true)
    public List<RequiredInformationResponseDto> findServiceSetupRequiredInformations(String countryCode) {
        List<RequiredInformation> requiredInformations = requiredInformationRepository.findAllByCountryCodeAndDeletedAtIsNullWithFile(
                countryCode);

        if (requiredInformations.isEmpty()) {
            return Collections.emptyList();
        }

        List<Criteria> allCriteria = criteriaRepository.findCommitmentCriteriaForRequiredInformations(requiredInformations,
                                                                                                      Criteria.Type.REQUIRED_INFORMATION,
                                                                                                      Criteria.Mode.COMMITMENT);

        Map<Integer, List<Criteria>> criteriaByInfoId = allCriteria.stream()
                .collect(Collectors.groupingBy(c -> c.getRequiredInformation().getId()));

        return requiredInformations.stream().map(info -> {
            RequiredInformationResponseDto dto = modelMapper.map(info, RequiredInformationResponseDto.class);

            if (info.getFiles() != null) {
                dto.setFile(modelMapper.map(info.getFiles(), FilesDto.class));
            }

            List<Criteria> relatedCriteria = criteriaByInfoId.getOrDefault(info.getId(), Collections.emptyList());

            List<CriteriaDto> criteriaDtos = relatedCriteria.stream()
                    .map(c -> modelMapper.map(c, CriteriaDto.class))
                    .toList();

            dto.setCriterias(criteriaDtos);
            dto.setHasCriteria(!relatedCriteria.isEmpty());

            return dto;
        }).toList();
    }

    /**
     * @ts-legacy This method translates a Prisma findMany query with a dynamic where clause.
     * The translation uses the JPA Specification API to build the query conditions dynamically
     * based on the provided optional parameters. A LEFT JOIN FETCH is added to the specification
     * to replicate the `include: { options: true }` behavior and prevent N+1 query problems.
     */
    @Transactional(readOnly = true)
    public List<CriteriaWithOptionsResponseDto> findServiceSetupCriterias(String countryCode,
                                                                          Criteria.Type type,
                                                                          Integer packagingServiceId,
                                                                          Integer requiredInformationId) {
        Specification<Criteria> spec = (root, query, cb) -> {
            // This ensures a single query is executed by fetching the collection eagerly.
            // It replicates `include: { options: true }` and prevents N+1 issues.
            // A distinct query is necessary to avoid duplicate parent entities.
            assert query != null;
            query.distinct(true);
            root.fetch("options", jakarta.persistence.criteria.JoinType.LEFT);

            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("type"), type));
            predicates.add(cb.isNull(root.get("deletedAt")));

            Join<Criteria, Country> countryJoin = root.join("country");
            predicates.add(cb.equal(countryJoin.get("code"), countryCode));

            if (packagingServiceId != null) {
                predicates.add(cb.equal(root.get("packagingService").get("id"), packagingServiceId));
            }

            if (requiredInformationId != null) {
                predicates.add(cb.equal(root.get("requiredInformation").get("id"), requiredInformationId));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        List<Criteria> criterias = criteriaRepository.findAll(spec);

        return criterias.stream()
                .map(criteria -> modelMapper.map(criteria, CriteriaWithOptionsResponseDto.class))
                .toList();
    }

    @Transactional(readOnly = true)
    public List<CriteriaWithOptionsResponseDto> findServiceSetupCommitment(String countryCode) {
        String upperCaseCountryCode = countryCode.toUpperCase();
        List<Criteria> commitment = criteriaRepository.findByCountryCodeAndModeWithWithOptions(
                upperCaseCountryCode,
                Criteria.Mode.COMMITMENT);

        if (commitment.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Commitment is empty");
        }

        return commitment.stream()
                .map(criteria -> modelMapper.map(criteria, CriteriaWithOptionsResponseDto.class))
                .toList();
    }

    /**
     * @ts-legacy The original Prisma query fetches a deeply nested object graph in a single database request.
     * Due to the complexity and potential for performance issues (e.g., Cartesian products, multiple bags)
     * with a single large JOIN FETCH query in JPA, this translation adopts a multi-query approach.
     * It first fetches the root `Country` entity, and related entities are loaded via their respective
     * lazy-loaded collections, which are assumed to be filtered for non-deleted records (e.g., using @Where).
     * This preserves the required data structure but alters the database access pattern from one complex query
     * to several simpler, on-demand queries.
     */
    @Transactional(readOnly = true)
    @SuppressWarnings({"java:S6541", "java:S3776"})
    public ServiceSetupCommitmentResponseDto submitServiceSetupCommitment(String countryCode,
                                                                          SubmitCommitmentDto data) {
        if (data.getYear() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Year is required");
        }
        if (data.getCommitment() == null || data.getCommitment().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Commitment answers array is required");
        }
        if (data.getCommitment().stream().anyMatch(c -> c.getId() == null || c.getAnswer() == null)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Commitment answers are invalid");
        }

        Country country = countryRepository.findByCode(countryCode.toUpperCase())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));

        List<Criteria> commitment = criteriaRepository.findByModeAndCountry_CodeAndDeletedAtIsNull(
                Criteria.Mode.COMMITMENT,
                countryCode.toUpperCase());

        if (commitment.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Commitment is empty");
        }

        final String LICENSE_YEAR = String.valueOf(data.getYear());

        CommitmentSubmitResultDto result = new CommitmentSubmitResultDto();
        result.setCountry(CommitmentResultCountryDto.builder()
                                  .id(country.getId())
                                  .name(country.getName())
                                  .code(country.getCode())
                                  .flagUrl(country.getFlagUrl())
                                  .authorizeRepresentativeObligated(country.getAuthorizeRepresentativeObligated())
                                  .otherCostsObligated(country.getOtherCostsObligated())
                                  .build());
        result.setYear(LICENSE_YEAR);
        result.setAuthorizeRepresentativeObligated(country.getAuthorizeRepresentativeObligated());
        result.setRepresentativeTier(null);
        result.setOtherCostsObligated(country.getOtherCostsObligated());
        result.setOtherCosts(new ArrayList<>());
        result.setRequiredInformations(new ArrayList<>(country.getRequiredInformations()));

        // Find the optional PriceList entity
        Optional<PriceList> priceListOpt = country.getCountryPriceLists()
                .stream()
                .filter(cpl -> cpl.getPriceList() != null && LICENSE_YEAR.equals(cpl.getPriceList()
                                                                                         .getConditionTypeValue()))
                .map(CountryPriceList::getPriceList)
                .findFirst();

        // Convert the entity to a DTO and set it on the result
        result.setPriceList(PriceListDto.fromEntity(priceListOpt.orElse(null)));

        List<CommitmentPackagingServiceDto> resultPackagingServices = new ArrayList<>();
        for (PackagingService packagingService : country.getPackagingServices()) {
            CommitmentPackagingServiceDto dto = CommitmentPackagingServiceDto.fromEntity(packagingService);

            boolean hasReportSetCriterias = commitment.stream()
                    .anyMatch(c -> c.getType() == Criteria.Type.REPORT_SET && packagingService.getId()
                            .equals(c.getPackagingServiceId()));

            dto.setReportSet(!hasReportSetCriterias && !packagingService.getReportSets()
                    .isEmpty() ? packagingService.getReportSets().getFirst() : null);

            boolean hasReportSetFrequencyCriterias = commitment.stream()
                    .anyMatch(c -> c.getType() == Criteria.Type.REPORT_FREQUENCY && packagingService.getId()
                            .equals(c.getPackagingServiceId()));

            ReportSetFrequency frequency = null;
            if (packagingService.getReportSetFrequencies().size() <= 1) {
                frequency = packagingService.getReportSetFrequencies().stream().findFirst().orElse(null);
            } else if (!hasReportSetFrequencyCriterias) {
                frequency = packagingService.getReportSetFrequencies().getFirst();
            }
            dto.setReportSetFrequency(toResponseDto(frequency));

            resultPackagingServices.add(dto);
        }
        result.setPackagingServices(resultPackagingServices);

        boolean hasOtherCostCriterias = commitment.stream().anyMatch(c -> c.getType() == Criteria.Type.OTHER_COST);
        if (!hasOtherCostCriterias) {
            result.setOtherCosts(new ArrayList<>(country.getOtherCosts()));
        }

        boolean hasRequiredInformationCriterias = commitment.stream()
                .anyMatch(c -> c.getType() == Criteria.Type.REQUIRED_INFORMATION);
        if (!hasRequiredInformationCriterias) {
            result.setRequiredInformations(new ArrayList<>(country.getRequiredInformations()));
        }

        for (Criteria criteria : commitment) {
            SubmitCommitmentDto.CommitmentAnswerDto submittedCriteria = data.getCommitment()
                    .stream()
                    .filter(c -> c.getId().equals(criteria.getId()))
                    .findFirst()
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.BAD_REQUEST,
                            "Missing answer for criteria " + criteria.getId()));
            boolean isValidAnswer = criteria.getOptions()
                    .stream()
                    .anyMatch(o -> o.getValue().equals(submittedCriteria.getAnswer()));
            if (!isValidAnswer) {
                throw new ResponseStatusException(
                        HttpStatus.BAD_REQUEST,
                                                  "Invalid answer for criteria " + criteria.getId());
            }

            switch (criteria.getType()) {
                case PACKAGING_SERVICE:
                    result.getPackagingServices()
                            .stream()
                            .filter(p -> p.getId().equals(criteria.getPackagingServiceId()))
                            .findFirst()
                            .ifPresent(p -> {
                                if (OBLIGED.equals(submittedCriteria.getAnswer())) {
                                    p.setObliged(true);
                                }
                            });
                    break;
                case REPORT_SET:
                    result.getPackagingServices()
                            .stream()
                            .filter(p -> p.getId().equals(criteria.getPackagingServiceId()))
                            .findFirst()
                            .ifPresent(resultPackagingService -> {
                                Integer selectedReportSetId = Integer.valueOf(submittedCriteria.getAnswer());
                                country.getPackagingServices()
                                        .stream()
                                        .filter(ps -> ps.getId().equals(criteria.getPackagingServiceId()))
                                        .findFirst()
                                        .flatMap(ps -> ps.getReportSets()
                                                .stream()
                                                .filter(rs -> rs.getId().equals(selectedReportSetId))
                                                .findFirst())
                                        .ifPresent(resultPackagingService::setReportSet);
                            });
                    break;
                case REPORT_FREQUENCY:
                    result.getPackagingServices()
                            .stream()
                            .filter(p -> p.getId().equals(criteria.getPackagingServiceId()))
                            .findFirst()
                            .ifPresent(resultPackagingService -> {
                                Integer selectedFrequencyId = Integer.valueOf(submittedCriteria.getAnswer());
                                country.getPackagingServices()
                                        .stream()
                                        .filter(ps -> ps.getId().equals(criteria.getPackagingServiceId()))
                                        .findFirst()
                                        .flatMap(ps -> ps.getReportSetFrequencies()
                                                .stream()
                                                .filter(freq -> freq.getId().equals(selectedFrequencyId))
                                                .findFirst())
                                        .ifPresent(selectedFreq -> {
                                            try {
                                                ReportSetFrequencyResponseDto freqDto = toResponseDto(selectedFreq);
                                                if (freqDto != null && selectedFreq.getFrequency() != null) {
                                                    Object parsedFrequency = objectMapper.readValue(
                                                            selectedFreq.getFrequency(),
                                                            Object.class);
                                                    freqDto.setFrequency(parsedFrequency);
                                                }
                                                resultPackagingService.setReportSetFrequency(freqDto);
                                            } catch (JsonProcessingException e) {
                                                throw new ResponseStatusException(
                                                        HttpStatus.INTERNAL_SERVER_ERROR,
                                                        "Failed to parse frequency JSON for criteria " + criteria.getId());
                                            }
                                        });
                            });
                    break;
                case AUTHORIZE_REPRESENTATIVE:
                    if (OBLIGED.equals(submittedCriteria.getAnswer())) {
                        result.setAuthorizeRepresentativeObligated(true);
                    }
                    break;
                case REPRESENTATIVE_TIER:
                    Integer tierId = Integer.valueOf(submittedCriteria.getAnswer());
                    country.getRepresentativeTiers()
                            .stream()
                            .filter(tier -> tier.getId().equals(tierId))
                            .findFirst()
                            .ifPresent(result::setRepresentativeTier);
                    break;
                case OTHER_COST:
                    Integer otherCostId = Integer.valueOf(submittedCriteria.getAnswer());
                    country.getOtherCosts()
                            .stream()
                            .filter(oc -> oc.getId().equals(otherCostId))
                            .findFirst()
                            .ifPresent(oc -> result.getOtherCosts().add(oc));
                    break;
                case REQUIRED_INFORMATION:
                    if ("REQUEST".equals(submittedCriteria.getAnswer())) {
                        country.getRequiredInformations()
                                .stream()
                                .filter(ri -> ri.getId().equals(criteria.getRequiredInformationId()))
                                .findFirst()
                                .ifPresent(ri -> {
                                    if (result.getRequiredInformations()
                                            .stream()
                                            .noneMatch(existingRi -> existingRi.getId().equals(ri.getId()))) {
                                        result.getRequiredInformations().add(ri);
                                    }
                                });
                    } else {
                        result.getRequiredInformations()
                                .removeIf(ri -> Objects.equals(ri.getId(), criteria.getRequiredInformationId()));
                    }
                    break;
                default:
                    // No action for other types in this logic block
                    break;
            }
        }

        List<FormattedCommitmentDto> formattedCommitment = commitment.stream().map(c -> {
            String answer = data.getCommitment()
                    .stream()
                    .filter(item -> item.getId().equals(c.getId()))
                    .findFirst()
                    .map(SubmitCommitmentDto.CommitmentAnswerDto::getAnswer)
                    .orElse(null);
            return new FormattedCommitmentDto(c, answer);
        }).toList();

        return new ServiceSetupCommitmentResponseDto(LICENSE_YEAR, result, formattedCommitment);
    }

    /**
     * Converts a {@link ReportSetFrequency} entity into its corresponding {@link ReportSetFrequencyResponseDto}.
     * <p>
     * This private helper method encapsulates the transformation logic, promoting code reuse and
     * simplifying the main service methods. It performs a direct, field-by-field mapping from
     * the source entity to the target DTO. A key feature is its null-safe design, returning
     * null immediately if the source entity is null.
     *
     * @param entity The source {@link ReportSetFrequency} entity to be converted. Can be null.
     * @return A new {@link ReportSetFrequencyResponseDto} instance populated with data from the
     * entity, or {@code null} if the source entity was null.
     */
    private ReportSetFrequencyResponseDto toResponseDto(ReportSetFrequency entity) {
        if (entity == null) {
            return null;
        }
        ReportSetFrequencyResponseDto dto = new ReportSetFrequencyResponseDto();
        dto.setId(entity.getId());
        dto.setRhythm(entity.getRhythm());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setDeletedAt(entity.getDeletedAt());
        dto.setPackagingServiceId(entity.getPackagingServiceId());
        dto.setFrequency(entity.getFrequency());
        return dto;
    }

    /**
     * @ts-legacy The original Prisma query uses a filtered include (`price_lists`) which fetches parent entities (`ReportSet`)
     * and only the children (`ReportSetPriceList`) that match the filter. Standard JPA `JOIN FETCH ... WHERE` clauses
     * would incorrectly filter the parent entities. This translation implements the equivalent behavior using a two-query
     * approach: first, it fetches the `ReportSet` entities based on the main criteria. Second, it fetches the filtered
     * `ReportSetPriceList` entities for those report sets and manually associates them in memory.
     */
    @SuppressWarnings({"java:S108", "java:S6541", "java:S3776", "java:S6916"})
    @Transactional(readOnly = true)
    public CalculateLicenseCostsResponseDto calculateLicenseCosts(String countryCode,
                                                                  CalculateLicenseCostsDto data) {
        final Integer year = data.getYear();
        final List<CalculateLicenseCostsDto.ReportSetDto> reportSetsDto = data.getReportSets();
        final List<Integer> reportSetIds = reportSetsDto.stream()
                .map(CalculateLicenseCostsDto.ReportSetDto::getId)
                .toList();

        Specification<ReportSet> spec = (root, query, cb) -> {
            assert query != null;
            query.distinct(true);
            Join<ReportSet, PackagingService> psJoin = root.join("packagingService", JoinType.INNER);
            Join<PackagingService, Country> cJoin = psJoin.join("country", JoinType.INNER);
            return cb.and(
                    cb.equal(cJoin.get("code"), countryCode.toUpperCase()),
                    reportSetIds.isEmpty() ? cb.conjunction() : root.get("id").in(reportSetIds));
        };

        List<ReportSet> foundReportSets = reportSetRepository.findAll(spec);
        if (foundReportSets.isEmpty() || (!reportSetIds.isEmpty() && foundReportSets.size() != reportSetIds.size())) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Report sets not found");
        }

        List<ReportSetPriceList> priceLists = reportSetPriceListRepository.findByReportSetInAndLicenseYearWithItems(foundReportSets,
                                                                                                                    year);
        Map<Integer, List<ReportSetPriceList>> priceListsByReportSetId = priceLists.stream()
                .collect(Collectors.groupingBy(pl -> pl.getReportSet().getId()));

        double totalLicenseCosts = 0;

        for (CalculateLicenseCostsDto.ReportSetDto rsDto : reportSetsDto) {
            List<ReportSetPriceList> relevantPriceLists = priceListsByReportSetId.get(rsDto.getId());

            if (relevantPriceLists == null || relevantPriceLists.isEmpty()) {
                continue;
            }
            ReportSetPriceList priceList = relevantPriceLists.getFirst();

            switch (priceList.getType()) {
                case FIXED_PRICE:
                    totalLicenseCosts += Optional.ofNullable(priceList.getFixedPrice()).orElse(0) / 100.0;
                    break;

                case PRICE_PER_CATEGORY, PRICE_PER_VOLUME_BASE_PRICE, PRICE_PER_VOLUME_MINIMUM_FEE:
                    List<ReportSetPriceListItem> items = (priceList.getType() == ReportSetPriceList.Type.PRICE_PER_CATEGORY) ? priceList.getItems()
                            .stream()
                            .filter(item -> item.getReportSetFraction().getLevel() == 1)
                            .toList() : priceList.getItems();

                    Map<String, ReportSetPriceListItem> itemMap = items.stream()
                            .collect(Collectors.toMap(ReportSetPriceListItem::getFractionCode, Function.identity()));

                    double fractionCostsInEur = rsDto.getFractions().stream().mapToDouble(fractionDto -> {
                        ReportSetPriceListItem item = itemMap.get(fractionDto.getCode());
                        if (item == null) {
                            return 0.0;
                        }
                        double priceInEurPerKg = item.getPrice() / 100.0;
                        double weightInKg = fractionDto.getWeight() / 1000.0;
                        return priceInEurPerKg * weightInKg;
                    }).sum();

                    if (priceList.getType() == ReportSetPriceList.Type.PRICE_PER_CATEGORY) {
                        totalLicenseCosts += fractionCostsInEur;
                    } else if (priceList.getType() == ReportSetPriceList.Type.PRICE_PER_VOLUME_BASE_PRICE) {
                        double basePriceInEur = Optional.ofNullable(priceList.getBasePrice()).orElse(0) / 100.0;
                        totalLicenseCosts += fractionCostsInEur + basePriceInEur;
                    } else { // PRICE_PER_VOLUME_MINIMUM_FEE
                        double minFeeInEur = Optional.ofNullable(priceList.getMinimumFee()).orElse(0) / 100.0;
                        totalLicenseCosts += Math.max(fractionCostsInEur, minFeeInEur);
                    }
                    break;
            }
        }

        double totalFractionWeightInGrams = reportSetsDto.stream()
                .flatMap(rs -> rs.getFractions().stream())
                .mapToDouble(CalculateLicenseCostsDto.FractionDto::getWeight)
                .sum();

        List<Criteria> calculatorCriterias = criteriaRepository.findByModeAndCountryCodeWithRelations(
                Criteria.Mode.CALCULATOR,
                countryCode.toUpperCase());

        CalculateLicenseCostsResponseDto response = new CalculateLicenseCostsResponseDto();
        response.setOtherCosts(new ArrayList<>());
        response.setRequiredInformations(new ArrayList<>());

        for (Criteria criteria : calculatorCriterias) {
            // Invert the first 'continue' condition into a positive check
            if (!SKIPPED_TYPES.contains(criteria.getType())) {
                findSelectedOption(
                        criteria,
                        totalLicenseCosts,
                        totalFractionWeightInGrams).ifPresent(selectedOption -> {
                    String optionValue = selectedOption.getValue();
                    switch (criteria.getType()) {
                        case AUTHORIZE_REPRESENTATIVE ->
                                response.setAuthorizeRepresentativeObligated(OBLIGED.equals(optionValue));
                        case REPRESENTATIVE_TIER -> representativeTierRepository.findById(Integer.parseInt(optionValue))
                                .ifPresent(response::setRepresentativeTier);
                        case OTHER_COST -> otherCostRepository.findById(Integer.parseInt(optionValue))
                                .ifPresent(cost -> response.getOtherCosts().add(cost));
                        case REQUIRED_INFORMATION -> {
                            if ("REQUEST".equals(optionValue) && criteria.getRequiredInformationId() != null) {
                                requiredInformationRepository.findById(criteria.getRequiredInformationId())
                                        .ifPresent(info -> response.getRequiredInformations().add(info));
                            }
                        }
                        default -> {
                        }
                    }
                });
            }
        }

        response.setLicenseCosts(totalLicenseCosts);
        return response;
    }

    private static final Set<Criteria.Type> SKIPPED_TYPES = Set.of(
            Criteria.Type.PACKAGING_SERVICE,
            Criteria.Type.REPORT_SET,
            Criteria.Type.REPORT_FREQUENCY);

    /**
     * Finds the first applicable {@link CriteriaOption} for a given calculator {@link Criteria}
     * by determining if a calculated value falls within the option's defined numerical range.
     * <p>
     * This helper method encapsulates the core logic of the calculator. It first selects the
     * appropriate value to compare (license costs or weight) based on the criterion's
     * {@code calculatorType}. It then iterates through the criterion's options, parsing the
     * string-based range values ({@code optionValue} and {@code optionToValue}) into numbers.
     * <p>
     * A key aspect of this implementation is the faithful translation of the original NestJS
     * application's unit handling logic, where {@code TOTAL_IN_KG} criteria were compared
     * against a value in grams, and {@code TOTAL_IN_TONS} against kilograms. This method
     * replicates that specific behavior.
     *
     * @param criteria                   The calculator criterion to evaluate, which contains the options and the calculator type.
     * @param licenseCosts               The total calculated license cost, used if the calculator type is {@code LICENSE_FEES}.
     * @param totalFractionWeightInGrams The total weight of all submitted fractions in grams, used for weight-based criteria.
     * @return An {@link Optional} containing the first {@link CriteriaOption} whose defined range
     * contains the calculated value. Returns {@link Optional#empty()} if no option matches
     * or if an option's range values are invalid and cannot be parsed.
     */
    private Optional<CriteriaOption> findSelectedOption(Criteria criteria,
                                                        double licenseCosts,
                                                        double totalFractionWeightInGrams) {
        if (criteria.getCalculatorType() == null) {
            return Optional.empty();
        }

        final double valueToCompare;
        switch (criteria.getCalculatorType()) {
            case LICENSE_FEES:
                valueToCompare = licenseCosts;
                break;
            case TOTAL_IN_KG:
                // original code compared against grams for this type.
                valueToCompare = totalFractionWeightInGrams;
                break;
            case TOTAL_IN_TONS:
                // original code compared against kg (grams/1000) for this type.
                valueToCompare = totalFractionWeightInGrams / 1000.0;
                break;
            default:
                return Optional.empty();
        }

        return criteria.getOptions().stream().filter(o -> {
            try {
                double from = Double.parseDouble(o.getOptionValue());
                double to = Double.parseDouble(o.getOptionToValue());
                // original's open interval check (< and >)
                return from < valueToCompare && valueToCompare < to;
            } catch (NumberFormatException | NullPointerException e) {
                return false;
            }
        }).findFirst();
    }
}