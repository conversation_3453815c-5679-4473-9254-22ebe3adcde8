package de.interzero.oneepr.admin.report_set_fractions;

import de.interzero.oneepr.admin.report_set.ReportSet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link ReportSetFraction} entities.
 */
@Repository
public interface ReportSetFractionRepository extends JpaRepository<ReportSetFraction, Integer> {

    /**
     * Finds all fractions that have not been soft-deleted.
     *
     * @return A list of active ReportSetFraction entities.
     */
    List<ReportSetFraction> findAllByDeletedAtIsNull();

    List<ReportSetFraction> findByReportSetInAndDeletedAtIsNullOrderByLevelAscOrderAsc(List<ReportSet> reportSets);

    /**
     * Finds a single fraction by its ID, but only if it has not been soft-deleted.
     *
     * @param id The ID of the fraction.
     * @return An Optional containing the fraction if found and active, otherwise empty.
     */
    Optional<ReportSetFraction> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * Finds all direct children of a given parent fraction ID.
     *
     * @param parentId The ID of the parent fraction.
     * @return A list of child ReportSetFraction entities.
     */
    List<ReportSetFraction> findByParentId(Integer parentId);

    /**
     * Finds a single ReportSetFraction by its unique code.
     *
     * @param code The unique code to search for.
     * @return An optional containing the found entity.
     */
    Optional<ReportSetFraction> findByCode(String code);

    @Modifying
    @Query("DELETE FROM ReportSetFraction f WHERE f.reportSet.id = :reportSetId")
    void deleteByReportSetId(@Param("reportSetId") Integer reportSetId);

    /**
     * Constructs the complete hierarchical tree of active fractions for a specific Report Set,
     * fetching up to three levels deep.
     * <p>
     * The query starts from the active, root fractions (those with no parent) and uses
     * {@code LEFT JOIN FETCH} to eagerly load their immediate children and grandchildren.
     * This approach is a key performance optimization, as it allows the entire nested
     * structure to be retrieved in a single database query, preventing N+1 problems.
     *
     * @param reportSetId The ID of the {@link ReportSet} for which to retrieve the fraction tree.
     * @return A list of root {@link ReportSetFraction} entities. Each entity in this list
     * represents a top-level node, with its {@code children} collections (and their
     * subsequent children) populated.
     */
    @Query(
            "SELECT f1 FROM ReportSetFraction f1  LEFT JOIN FETCH f1.children f2  LEFT JOIN FETCH f2.children f3  WHERE f1.reportSet.id = :reportSetId AND f1.deletedAt IS NULL AND f1.parent IS NULL"
    )
    List<ReportSetFraction> findActiveFractionTreeByReportSetId(@Param("reportSetId") Integer reportSetId);

    long countByReportSet_IdAndDeletedAtIsNull(Integer reportSetId);

    /**
     * Fetches the top-level (parent is null) ReportSetFractions for a given ReportSet ID.
     * It eagerly fetches the associated fraction icon. The results are explicitly ordered by the 'order' column.
     * Note: This query does not fetch nested children to avoid N+1 problems in a single query; nested children
     * are loaded by JPA's lazy-loading mechanism if accessed. For fully populated trees, a different strategy is needed.
     * However, to translate the original query's intent of ordering top-level and nested items, we handle ordering
     * at each level where data is explicitly fetched. In this case, the top-level fractions are ordered.
     * The nested children ordering would rely on subsequent lazy loads or separate queries if needed.
     */
    @Query(
            "SELECT DISTINCT f FROM ReportSetFraction f  LEFT JOIN FETCH f.fractionIcon  WHERE f.reportSet.id = :reportSetId  AND f.deletedAt IS NULL AND f.parent IS NULL  ORDER BY f.order ASC"
    )
    // Ordering applied here
    List<ReportSetFraction> findTopLevelActiveFractionsWithChildrenByReportSetId(@Param("reportSetId") Integer reportSetId);

    List<ReportSetFraction> findByReportSet_Id(Integer id);
}