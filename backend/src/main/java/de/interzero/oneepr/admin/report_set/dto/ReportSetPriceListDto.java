package de.interzero.oneepr.admin.report_set.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * This DTO represents the data required to create or update a ReportSetPriceList.
 * It is a direct translation of the price_list objects expected in the update method's payload.
 * It contains the price list's own properties and a nested list of its items.
 */
@Getter
@Setter
public class ReportSetPriceListDto extends BaseDto {

    @Schema(
            description = "The unique identifier of the price list. Omit or set to null for creation.",
            example = "301"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "The title of the price list.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "2025 Standard Rates"
    )
    @JsonProperty("title")
    @NotNull
    private String title;

    @Schema(
            description = "The start date of the price list's validity.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "2025-01-01",
            format = "date"
            )
    @JsonProperty("start_date")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @Schema(
            description = "The end date of the price list's validity.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "2025-12-31",
            format = "date"
    )
    @JsonProperty("end_date")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @Schema(
            description = "The base price component, in cents, if applicable.",
            example = "5000"
    )
    @JsonProperty("base_price")
    private Integer basePrice;

    @Schema(
            description = "The fixed price component, in cents, if applicable.",
            example = "10000"
    )
    @JsonProperty("fixed_price")
    private Integer fixedPrice;

    @Schema(
            description = "The minimum fee component, in cents, if applicable.",
            example = "1200"
    )
    @JsonProperty("minimum_fee")
    private Integer minimumFee;

    @Schema(
            description = "The pricing model type for this list.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = ReportSetPriceList.Type.class,
            example = "PRICE_PER_VOLUME_BASE_PRICE"
    )
    @JsonProperty("type")
    @NotNull
    private ReportSetPriceList.Type type;

    @Schema(
            description = "The license year this price list applies to.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "2025"
    )
    @JsonProperty("license_year")
    @NotNull
    private Integer licenseYear;

    @Schema(description = "A list of items containing prices for specific fractions.")
    @JsonProperty("items")
    private List<ReportSetPriceListItemDto> items;
}