package de.interzero.oneepr.admin.fraction_icon;

import de.interzero.oneepr.admin.fraction_icon.dto.CreateFractionIconDto;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service for managing fraction icons.
 */
@Service
@RequiredArgsConstructor
public class FractionIconService {

    private final FractionIconRepository fractionIconRepository;

    private final FilesRepository filesRepository;

    /**
     * Creates a new fraction icon from a given file ID.
     *
     * @param data The DTO containing the file_id.
     * @return The newly created FractionIcon entity.
     * @ts-legacy The `mapPresentFields` utility is not used here because this method does not map fields
     * directly from the DTO. Instead, it performs a "lookup-and-construct" operation: it uses the `file_id`
     * to find a File entity and then builds a new FractionIcon with derived data (like the imageUrl)
     * and new timestamps.
     */
    @Transactional
    public FractionIcon create(CreateFractionIconDto data) {
        Files file = filesRepository.findById(data.getFileId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "File not found"));

        FractionIcon fractionIcon = new FractionIcon();
        fractionIcon.setFile(file);
        fractionIcon.setImageUrl(file.getName());
        fractionIcon.setCreatedAt(Instant.now());
        fractionIcon.setUpdatedAt(Instant.now());

        return fractionIconRepository.save(fractionIcon);
    }

    /**
     * Finds all non-deleted fraction icons.
     *
     * @return A list of all active fraction icons.
     */
    @Transactional(readOnly = true)
    public List<FractionIcon> findAll() {
        return fractionIconRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Finds a single non-deleted fraction icon by its ID.
     *
     * @param id The ID of the fraction icon to find.
     * @return The found FractionIcon entity.
     */
    @Transactional(readOnly = true)
    public FractionIcon findOne(Integer id) {
        return fractionIconRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Fraction icon not found"));
    }

    /**
     * Soft-deletes a fraction icon by setting its deleted_at timestamp.
     *
     * @param id The ID of the fraction icon to remove.
     */
    @Transactional
    public FractionIcon remove(Integer id) {
        FractionIcon fractionIcon = fractionIconRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Fraction icon not found"));

        fractionIcon.setDeletedAt(Instant.now());
        return fractionIconRepository.save(fractionIcon);
    }
}
