package de.interzero.oneepr.admin.service_setup;

import de.interzero.oneepr.admin.criteria.Criteria;
import de.interzero.oneepr.admin.service_setup.dto.*;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import de.interzero.oneepr.admin.report_set.dto.ReportSetDetailDto;

import java.util.List;

@RestController
@RequestMapping(Api.SERVICE_SETUPS)
@Tag(name = "ServiceSetup")
@RequiredArgsConstructor
@Secured({Role.<PERSON>, Role.SUPER_ADMIN, Role.CLERK})
public class ServiceSetupController {

    private final ServiceSetupService serviceSetupService;

    @GetMapping("/{countryCode}")
    @Operation(summary = "Get service setup by country code")
    @Secured({"ROLE_SUPER_ADMIN", "ROLE_ADMIN", "ROLE_CLERK"})
    public ServiceSetupDto findServiceSetup(@PathVariable String countryCode) {
        return this.serviceSetupService.findServiceSetup(countryCode);
    }

    @GetMapping("/{countryCode}/status")
    @Operation(summary = "Get service setup status by country code")
    public ServiceSetupStatusDto getServiceSetupStatus(@PathVariable String countryCode) {
        return serviceSetupService.getServiceSetupStatus(countryCode);
    }

    @GetMapping("/{countryCode}/packaging-services")
    @Operation(summary = "Get service setup packaging services")
    public List<PackagingServiceSetupDto> findServiceSetupPackagingServices(@PathVariable String countryCode) {
        return serviceSetupService.findServiceSetupPackagingServices(countryCode);
    }

    @GetMapping("/{countryCode}/report-sets")
    @Operation(summary = "Get service setup report sets")
    public List<ReportSetSetupDto> findServiceSetupReportSets(@PathVariable String countryCode) {
        return serviceSetupService.findServiceSetupReportSets(countryCode);
    }

    /**
     * Finds a single, detailed report set including its full graph of related entities
     * for the service setup administration interface.
     *
     * @param countryCode The code of the country to which the report set belongs.
     * @param reportSetId The string ID of the report set, which will be parsed into an integer.
     * @return A detailed DTO containing the full ReportSet graph.
     * @throws ResponseStatusException if the reportSetId is not a valid integer.
     */
    @GetMapping("/{countryCode}/report-sets/{reportSetId}")
    @Operation(summary = "Get service setup report set")
    public ReportSetDetailDto findServiceSetupReportSet(@PathVariable String countryCode,
                                                        @PathVariable String reportSetId) {
        return serviceSetupService.findServiceSetupReportSet(countryCode, Integer.valueOf(reportSetId));
    }


    @GetMapping("/{countryCode}/report-frequencies")

    @Operation(summary = "Get service setup report frequencies")
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved report frequencies"
    )
    public List<ReportSetFrequencyResponseDto> findServiceSetupReportFrequencies(@PathVariable("countryCode") String countryCode) {
        return this.serviceSetupService.findServiceSetupReportFrequencies(countryCode);
    }

    @GetMapping("/{countryCode}/representative-tiers")
    @Operation(summary = "Get service setup representative tiers")
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved representative tiers"
    )
    public List<RepresentativeTierResponseDto> findServiceSetupRepresentativeTiers(@PathVariable("countryCode") String countryCode) {
        return this.serviceSetupService.findServiceSetupRepresentativeTiers(countryCode);
    }

    @GetMapping("/{countryCode}/other-costs")
    @Operation(summary = "Get service setup other costs")
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved other costs"
    )
    public List<OtherCostResponseDto> findServiceSetupOtherCosts(@PathVariable("countryCode") String countryCode) {
        return this.serviceSetupService.findServiceSetupOtherCosts(countryCode);
    }

    @GetMapping("/{countryCode}/price-lists")
    @Operation(summary = "Get service setup price lists")
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved price lists"
    )
    public List<CountryPriceListResponseDto> findServiceSetupPriceLists(@PathVariable("countryCode") String countryCode) {
        return this.serviceSetupService.findServiceSetupPriceLists(countryCode);
    }

    @GetMapping("/{countryCode}/required-informations")
    @Operation(summary = "Get service setup required informations")
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved required informations"
    )
    public List<RequiredInformationResponseDto> findServiceSetupRequiredInformations(@PathVariable("countryCode") String countryCode) {
        return this.serviceSetupService.findServiceSetupRequiredInformations(countryCode);
    }

    @GetMapping("/{countryCode}/criterias")
    @Operation(summary = "Get service setup criterias with dynamic filters")
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved criterias"
    )
    public List<CriteriaWithOptionsResponseDto> findServiceSetupCriterias(@PathVariable("countryCode") String countryCode,
                                                                          @RequestParam("type") Criteria.Type type,
                                                                          @RequestParam(
                                                                                  name = "required_information_id",
                                                                                  required = false
                                                                          ) Integer requiredInformationId,
                                                                          @RequestParam(
                                                                                  name = "packaging_service_id",
                                                                                  required = false
                                                                          ) Integer packagingServiceId) {
        return this.serviceSetupService.findServiceSetupCriterias(
                countryCode,
                type,
                packagingServiceId,
                requiredInformationId);
    }

    @GetMapping("/{countryCode}/commitment")
    @Operation(summary = "Get service setup commitment")
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved commitment criterias"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Commitment is empty for the given country"
    )
    @PreAuthorize("permitAll()")
    public List<CriteriaWithOptionsResponseDto> findServiceSetupCommitment(@PathVariable("countryCode") String countryCode) {
        return this.serviceSetupService.findServiceSetupCommitment(countryCode);
    }

    @PostMapping("/{countryCode}/commitment")
    @Operation(summary = "Submit service setup commitment")
    @PreAuthorize("permitAll()")
    public ServiceSetupCommitmentResponseDto submitServiceSetupCommitment(@PathVariable String countryCode,
                                                                          @RequestBody SubmitCommitmentDto data) {
        return serviceSetupService.submitServiceSetupCommitment(countryCode, data);
    }

    @PostMapping("/{countryCode}/calculator")
    @Operation(summary = "Calculate license costs")
    @PreAuthorize("permitAll()")
    public CalculateLicenseCostsResponseDto calculateLicenseCosts(@PathVariable String countryCode,
                                                                  @RequestBody CalculateLicenseCostsDto data) {
        return serviceSetupService.calculateLicenseCosts(countryCode, data);
    }
}