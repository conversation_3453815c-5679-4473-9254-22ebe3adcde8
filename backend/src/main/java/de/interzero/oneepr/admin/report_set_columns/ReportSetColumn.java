package de.interzero.oneepr.admin.report_set_columns;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set.ReportSet;
import de.interzero.oneepr.admin.report_set_column_fractions.ReportSetColumnFraction;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "report_set_column",
        schema = "public"
)
public class ReportSetColumn {

    public enum UnitType {
        KG,
        UNITS,
        EACH
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("description")
    private String description;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "unit_type",
            nullable = false
    )
    @JsonProperty("unit_type")
    private UnitType unitType;

    @Column(name = "parent_id")
    @JsonProperty("parent_id")
    private Integer parentId;

    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @Column(
            name = "level",
            nullable = false
    )
    @JsonProperty("level")
    private Integer level = 1;

    /**
     * @ts-legacy Escaped quotes are required for reserved SQL keywords
     */
    @Column(
            name = "\"order\"",
            nullable = false
    )
    @JsonProperty("order")
    private Integer order = 1;

    @Column(
            name = "code",
            nullable = false,
            unique = true
    )
    @JsonProperty("code")
    private String code;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "parent_code",
            referencedColumnName = "code"
    )
    @JsonIgnore
    @JsonProperty("parent")
    private ReportSetColumn parent;

    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @JoinColumn(
            name = "report_set_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("report_set")
    private ReportSet reportSet;

    @OneToMany(mappedBy = "parent")
    @JsonIgnore
    @JsonProperty("children")
    private List<ReportSetColumn> children = new ArrayList<>();

    @OneToMany(mappedBy = "reportSetColumn")
    @JsonIgnore
    @JsonProperty("fractions")
    private List<ReportSetColumnFraction> fractions = new ArrayList<>();

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    // --- Transient Getters for Foreign Keys ---

    @Transient
    @JsonProperty("report_set_id")
    public Integer getReportSetId() {
        return (this.reportSet != null) ? this.reportSet.getId() : null;
    }

    @Transient
    @JsonProperty("parent_code")
    public String getParentCode() {
        return (this.parent != null) ? this.parent.getCode() : null;
    }

    // --- Bi-directional relationship helper methods ---

    /**
     * Adds a child column and synchronizes the bidirectional relationship.
     *
     * @param child The child {@link ReportSetColumn} to add.
     */
    public void addChild(ReportSetColumn child) {
        this.children.add(child);
        child.setParent(this);
    }

    /**
     * Removes a child column and synchronizes the bidirectional relationship.
     *
     * @param child The child {@link ReportSetColumn} to remove.
     */
    public void removeChild(ReportSetColumn child) {
        this.children.remove(child);
        child.setParent(null);
    }

    /**
     * Adds a column-fraction link and synchronizes the bidirectional relationship.
     *
     * @param fraction The {@link ReportSetColumnFraction} link to add.
     */
    public void addFraction(ReportSetColumnFraction fraction) {
        this.fractions.add(fraction);
        fraction.setReportSetColumn(this);
    }

    /**
     * Removes a column-fraction link and synchronizes the bidirectional relationship.
     *
     * @param fraction The {@link ReportSetColumnFraction} link to remove.
     */
    public void removeFraction(ReportSetColumnFraction fraction) {
        this.fractions.remove(fraction);
        fraction.setReportSetColumn(null);
    }
}