package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * A helper Data Transfer Object created to represent the nested 'packaging_service' object
 * within the ReportSetFrequencyResponseDto.
 * <p>
 * It originates from the 'include' statement in the original Prisma query, which selectively
 * included 'criterias' that were filtered by type and mode. This DTO is necessary to
 * structure the JSON response to match the shape produced by the NestJS service.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PackagingServiceWithCriteriaDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("criterias")
    private List<CriteriaDto> criterias;
}