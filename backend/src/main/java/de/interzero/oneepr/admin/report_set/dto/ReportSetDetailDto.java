package de.interzero.oneepr.admin.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set.ReportSet;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * This DTO is a direct translation of the response from the find/update ReportSet methods.
 * It is used to represent the complete, detailed data structure of a single report set,
 * including all its deeply nested relationships, solving lazy loading and serialization issues.
 * It is distinct from the ReportSet entity, which is not intended for direct serialization.
 */
@Data
public class ReportSetDetailDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("mode")
    private ReportSet.ReportSetMode mode;

    @JsonProperty("type")
    private ReportSet.ReportSetType type;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @JsonProperty("sheet_file_description")
    private String sheetFileDescription;

    @JsonProperty("sheet_file")
    private FilesDetailDto sheetFile;

    @JsonProperty("has_criteria")
    private boolean hasCriteria;

    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @JsonProperty("packaging_service")
    private PackagingServiceDto packagingService;

    @JsonProperty("columns")
    private List<ReportSetColumnDetailDto> columns;

    @JsonProperty("fractions")
    private List<ReportSetFractionDetailDto> fractions;

    @JsonProperty("price_lists")
    private List<ReportSetPriceListDetailDto> priceLists;
}