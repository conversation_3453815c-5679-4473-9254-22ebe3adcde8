package de.interzero.oneepr.admin.report_set_columns;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.report_set.ReportSet;
import de.interzero.oneepr.admin.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.report_set_columns.dto.CreateReportSetColumnDto;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.UUID;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link ReportSetColumnsController}.
 * This class validates the full HTTP request-response cycle for the report set column module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ReportSetColumnControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReportSetRepository reportSetRepository;

    @Autowired
    private ReportSetColumnRepository reportSetColumnRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    @Autowired
    private CountryRepository countryRepository;

    private ReportSet testReportSet;

    private ReportSetColumn testReportSetColumn;

    /**
     * Sets up a consistent and valid database state before each test method runs.
     */
    @BeforeEach
    void setUp() {
        // Clear repositories in reverse order of dependency to avoid constraint violations
        reportSetColumnRepository.deleteAll();
        reportSetRepository.deleteAll();
        packagingServiceRepository.deleteAll();
        countryRepository.deleteAll();

        // Create the required chain of entities
        Country country = createAndSaveTestCountry();
        PackagingService packagingService = createAndSaveTestPackagingService(country);
        testReportSet = createAndSaveTestReportSet(packagingService);
        testReportSetColumn = createAndSaveTestReportSetColumn("Top Level Column", testReportSet, null);
    }

    /**
     * Verifies that a POST request successfully creates a new top-level column.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewTopLevelColumn() throws Exception {
        CreateReportSetColumnDto createDto = new CreateReportSetColumnDto();
        createDto.setName("New Column");
        createDto.setDescription("A brand new column");
        createDto.setUnitType(ReportSetColumn.UnitType.KG);
        createDto.setReportSetId(testReportSet.getId());

        ResultActions resultActions = mockMvc.perform(post(Api.REPORT_SET_COLUMNS).contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(createDto)));
        resultActions.andExpect(status().isOk())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("New Column")))
                .andExpect(jsonPath("$.report_set_id", is(testReportSet.getId())))
                .andExpect(jsonPath("$.parent_id").doesNotExist());
    }

    /**
     * Verifies that a POST request with an invalid Report Set ID returns a Not Found error.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_withInvalidReportSetId_shouldReturnNotFound() throws Exception {
        CreateReportSetColumnDto createDto = new CreateReportSetColumnDto();
        createDto.setName("Orphan Column");
        createDto.setDescription("This will fail");
        createDto.setUnitType(ReportSetColumn.UnitType.EACH);
        createDto.setReportSetId(99999); // Non-existent ID

        mockMvc.perform(post(Api.REPORT_SET_COLUMNS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isNotFound());
    }

    /**
     * Verifies that a GET request returns all active report set columns.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnListOfColumns() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_COLUMNS))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testReportSetColumn.getId())));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct column.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnCorrectColumn() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_COLUMNS + "/{id}", testReportSetColumn.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testReportSetColumn.getId())))
                .andExpect(jsonPath("$.name", is("Top Level Column")));
    }

    /**
     * Verifies that a DELETE request correctly soft-deletes a parent column and its direct children.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void remove_shouldSoftDeleteColumnAndItsChildren() throws Exception {
        // Setup: Create a child column linked to the main test column
        ReportSetColumn childColumn = createAndSaveTestReportSetColumn(
                "Child Column",
                testReportSet,
                testReportSetColumn);

        // Action: Delete the parent column
        mockMvc.perform(delete(Api.REPORT_SET_COLUMNS + "/{id}", testReportSetColumn.getId()))
                .andExpect(status().isOk());

        // Verification 1: The parent column should now be "not found" via the API
        mockMvc.perform(get(Api.REPORT_SET_COLUMNS + "/{id}", testReportSetColumn.getId()))
                .andExpect(status().isNotFound());

        // Verification 2: The child column should also be "not found" via the API
        mockMvc.perform(get(Api.REPORT_SET_COLUMNS + "/{id}", childColumn.getId())).andExpect(status().isNotFound());

        // Verification 3: Both should have their deletedAt timestamp set in the database
        ReportSetColumn deletedParent = reportSetColumnRepository.findById(testReportSetColumn.getId()).orElseThrow();
        ReportSetColumn deletedChild = reportSetColumnRepository.findById(childColumn.getId()).orElseThrow();

        assertNotNull(deletedParent.getDeletedAt());
        assertNotNull(deletedChild.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        country.setCreatedAt(Instant.now());
        return countryRepository.saveAndFlush(country);
    }

    private PackagingService createAndSaveTestPackagingService(Country country) {
        PackagingService service = new PackagingService();
        service.setName("Test Service");
        service.setDescription("A test packaging service");
        service.setCountry(country);
        return packagingServiceRepository.saveAndFlush(service);
    }

    private ReportSet createAndSaveTestReportSet(PackagingService packagingService) {
        ReportSet reportSet = new ReportSet();
        reportSet.setName("Test Report Set 2025");
        reportSet.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        reportSet.setType(ReportSet.ReportSetType.FRACTIONS);
        reportSet.setPackagingService(packagingService);
        return reportSetRepository.saveAndFlush(reportSet);
    }

    private ReportSetColumn createAndSaveTestReportSetColumn(String name,
                                                             ReportSet reportSet,
                                                             ReportSetColumn parent) {
        ReportSetColumn column = new ReportSetColumn();
        column.setName(name);
        column.setDescription("Description for " + name);
        column.setUnitType(ReportSetColumn.UnitType.KG);
        column.setCode(UUID.randomUUID().toString());
        column.setReportSet(reportSet);
        if (parent != null) {
            column.setParent(parent);
            column.setParentId(parent.getId());
        }
        return reportSetColumnRepository.saveAndFlush(column);
    }
}