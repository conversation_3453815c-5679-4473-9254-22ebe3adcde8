{"customers": [{"id": 2182, "first_name": "<PERSON>", "last_name": "Helena", "salutation": "Mr.", "email": "<EMAIL>", "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-16T18:30:02.671Z", "updated_at": "2025-07-16T18:33:36.719Z", "company_name": "Caffe <PERSON>", "language": null, "currency": "EUR", "companies": [{"id": 1228, "name": "Caffe <PERSON>", "description": null, "vat": null, "tin": "847465485454", "lucid": null, "starting": null, "website": null, "industry_sector": null, "owner_name": null, "created_at": "2025-07-16T18:33:32.768Z", "updated_at": "2025-07-16T18:33:32.768Z", "address": {"id": 1228, "country_code": "PT", "address_line": "Sample Address Line", "city": "Sample City", "zip_code": "12345", "street_and_number": "Sample Street 1", "additional_address": "", "created_at": "2025-07-16T18:33:32.768Z", "updated_at": "2025-07-16T18:33:32.768Z"}, "emails": [], "billing": null}], "phones": [{"id": 3738, "phone_number": "+49887 4654-9854", "phone_type": "PHONE", "created_at": "2025-07-16T18:33:36.693Z", "updated_at": "2025-07-16T18:33:36.693Z"}, {"id": 3739, "phone_number": "", "phone_type": "PHONE", "created_at": "2025-07-16T18:33:36.693Z", "updated_at": "2025-07-16T18:33:36.693Z"}], "contracts": [{"id": 1012, "type": "EU_LICENSE", "status": "ACTIVE", "title": "EU License Contract", "start_date": "2025-01-01T00:00:00Z", "end_date": "2025-08-06T07:44:48.798Z", "created_at": "2025-07-16T18:30:02.671Z", "updated_at": "2025-07-16T18:33:36.719Z", "licenses": [{"id": 2013, "country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "created_at": "2025-07-16T18:30:02.671Z", "updated_at": "2025-07-16T18:33:36.719Z", "termination": null, "files": [], "certificates": []}, {"id": 2014, "country_id": 1, "country_code": "AT", "country_name": "Austria", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/at.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "created_at": "2025-07-16T18:30:02.671Z", "updated_at": "2025-07-16T18:33:36.719Z", "termination": null, "files": [], "certificates": []}], "action_guides": [], "termination": null}, {"id": 1013, "type": "ACTION_GUIDE", "status": "ACTIVE", "title": "Action Guide Contract", "start_date": "2025-01-01T00:00:00Z", "end_date": "2025-08-06T07:44:48.798Z", "created_at": "2025-07-16T18:30:02.671Z", "updated_at": "2025-07-16T18:33:36.719Z", "licenses": [], "action_guides": [{"country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "contract_status": "ACTIVE", "termination": null, "created_at": "2025-07-16T18:30:02.671Z", "updated_at": "2025-07-16T18:33:36.719Z"}], "termination": null}], "type": "REGULAR", "user_id": 4671}], "pages": 1, "count": 8, "current_page": 1}